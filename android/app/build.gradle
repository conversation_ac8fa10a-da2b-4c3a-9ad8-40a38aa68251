plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader("UTF-8") { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty("flutter.versionCode")
if (flutterVersionCode == null) {
    flutterVersionCode = "1"
}

def flutterVersionName = localProperties.getProperty("flutter.versionName")
if (flutterVersionName == null) {
    flutterVersionName = "1.0"
}

android {
    namespace = "com.marspt.xiaopa"
    compileSdk 35

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.marspt.xiaopa"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 23
        //没事不要升级
        targetSdkVersion 34
        versionCode = flutterVersionCode.toInteger()
        versionName = flutterVersionName
        ndk {
            //noinspection ChromeOsAbiSupport
            abiFilters "arm64-v8a"
        }

        manifestPlaceholders = [
                JPUSH_PKGNAME : "com.marspt.xiaopa",
                JPUSH_APPKEY : "8053706136cfb820e1b48fe4", // NOTE: JPush 上注册的包名对应的 Appkey.
                JPUSH_CHANNEL : "developer-default", //暂时填写默认值即可.

                VIVO_APPKEY : "0c91f9da3f4ef49a944a9f08ee83c1fc", // VIVO平台注册的appkey
                VIVO_APPID : "105917888", // VIVO平台注册的appid
        ]
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }

    signingConfigs {
        release {
            storeFile file('/Users/<USER>/Desktop/Project/shkj.jks')
            storePassword '123456'
            keyAlias 'shkj'
            keyPassword '123456'
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.cfg'

            minifyEnabled true
            shrinkResources true
        }
        debug {
            signingConfig signingConfigs.debug
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.cfg'
        }
    }


    /*
        image_pickers 的是 ucrop:v3.11.2。
        image_cropper 的是 ucrop:2.2.10，所以这两个插件冲突了
        排除 image_pickers 插件中使用的 ucrop-v3.11.2：
    */
    configurations.configureEach {
        resolutionStrategy.eachDependency { DependencyResolveDetails details ->
            if (details.requested.group == 'io.github.lucksiege' && details.requested.name == 'ucrop') {
                details.useTarget 'com.github.yalantis:ucrop:2.2.10'
            }
        }
    }

    // 加入这个配置，以防使用 AndroidX 35+ API
    packagingOptions {
        exclude "META-INF/AL2.0"
        exclude "META-INF/LGPL2.1"
    }
}


flutter {
    source = "../.."
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'
    implementation 'com.bytedance.speechengine:speechengine_tob:0.0.4'
    implementation 'io.reactivex.rxjava3:rxjava:3.1.4'
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.0'


    // Glide 图片加载核心库
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    // Glide 编译时注解处理器（必须添加！）
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'

    implementation project(":lib-blufi")

    api 'com.tencent.mm.opensdk:wechat-sdk-android:6.8.34'

    implementation fileTree(dir: 'libs', includes: ['*.jar','*.aar'])

    //厂商版本和 JPush SDK 版本保持一致
//    implementation 'cn.jiguang.sdk.plugin:vivo:5.8.0'
}