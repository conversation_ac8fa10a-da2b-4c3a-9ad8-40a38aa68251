import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

class DeviceUpdateDialog extends StatefulWidget {
  final String upDateContent;
  final Function? onTap;

  const DeviceUpdateDialog({super.key, this.upDateContent = '', this.onTap});

  @override
  State<DeviceUpdateDialog> createState() => _DeviceUpdateDialogState();
}

class _DeviceUpdateDialogState extends State<DeviceUpdateDialog> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Images(
            path: R.console_update_bg_png,
            width: 295.w,
            height: 180.h,
            boxFit: BoxFit.fill,
          ),
          Container(
            constraints: BoxConstraints(minHeight: 120.h),
            width: 295.w,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(10.r),
                    bottomRight: Radius.circular(10.r)),
                color: Colors.white),
            child: Column(
              children: [
                Container(
                  margin: EdgeInsets.only(
                      left: 29.w, right: 22.w, top: 5.h, bottom: 19.h),
                  child: Text(
                    widget.upDateContent,
                    textAlign: TextAlign.center,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.authenticationTextColor,
                        fontSize: 14),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(bottom: 20.h),
                  width: 200.w,
                  height: 48.h,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24.r),
                      color: const Color.fromRGBO(40, 39, 46, 1)),
                  child: Center(
                    child: ThemeText(
                      dataStr: "confirm".tr,
                      keyName: 'textColor',
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                ).inkWell(() => widget.onTap?.call())
              ],
            ),
          )
        ],
      ),
    );
  }
}
