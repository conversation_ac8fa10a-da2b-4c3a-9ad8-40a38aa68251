import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'console_setting_logic.dart';
import 'console_setting_state.dart';

class ConsoleSettingPage extends BaseCommonView {
  ConsoleSettingPage({super.key});

  final ConsoleSettingLogic logic = Get.put(ConsoleSettingLogic());
  final ConsoleSettingState state = Get.find<ConsoleSettingLogic>().state;

  @override
  String? get navTitle => "console_setting_title".tr;

  @override
  TextStyle? get navTitleStyle =>
      StyleConfig.blackStyle(fontSize: 18, fontWeight: FontWeight.w500);

  @override
  Color? get navColor => Colors.white;

  @override
  double? get leftWidth => 40.w;

  @override
  Widget? get leftButton => Padding(
        padding: EdgeInsets.only(left: 15.w),
        child: Images(
          path: R.back_png,
          width: 20.w,
          height: 20.h,
          scale: 2.8,
        ).inkWell(() => Get.back()),
      );

  @override
  Color? get contentColor => const Color.fromRGBO(249, 249, 249, 1);

  @override
  Widget buildContent() {
    return GetBuilder<ConsoleSettingLogic>(
      id: "console_setting_view",
      builder: (_) => createCommonView(
        logic,
        (_) {
          return Column(
            children: [
              Container(
                width: 1.sw,
                height: 127.h,
                color: Colors.white,
                child: Row(
                  children: [
                    SizedBox(width: 20.w),
                    SizedBox(
                      width: 100.r,
                      height: 100.r,
                      // padding: EdgeInsets.all(23.r),
                      // decoration: BoxDecoration(
                      //     image: DecorationImage(
                      //         image: AssetImage(ImageConfig.themeIcon(
                      //             image1: R.console_setting_image_01_png,
                      //             image2: R.console_setting_image_01_pink_png)),
                      //         fit: BoxFit.fill)),
                      // child: Images(
                      //   path: ImageConfig.themeIcon(
                      //       image1: R.console_bind_image_03_png,
                      //       image2: R.console_bind_image_03_pink_png),
                      // ),
                      child: Stack(
                        children: [
                          const Positioned.fill(
                            child: ThemeImagePath(
                                fileName: 'console_setting_image_01.png'),
                          ),
                          Positioned.fill(
                            child: Container(
                              padding: EdgeInsets.all(23.r),
                              child: const ThemeImagePath(
                                  fileName: 'console_bind_image_03.png'),
                            ),
                          )
                        ],
                      ),
                    ),
                    SizedBox(width: 7.w),
                    Container(
                      margin: EdgeInsets.only(top: 17.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "${"console_name".tr}_${state.model?.mac?.toUpperCase()}",
                            style: StyleConfig.otherStyle(
                                color: const Color.fromRGBO(69, 67, 74, 1),
                                fontWeight: FontWeight.w600),
                          ),
                          SizedBox(height: 4.h),
                          Row(
                            children: [
                              Text(
                                'ID：${state.model?.id ?? ""}',
                                style: StyleConfig.otherStyle(
                                    color: ColorConfig.authenticationTextColor,
                                    fontWeight: FontWeight.w400,
                                    fontSize: 14),
                              ),
                              SizedBox(width: 10.w),
                              Images(
                                path: R.mine_copy_png,
                                width: 14.r,
                                height: 14.r,
                              )
                            ],
                          ).inkWell(() => logic.copyAction()),
                          SizedBox(height: 10.h),
                          Container(
                            width: 90.w,
                            height: 24.h,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12.r),
                                color: const Color.fromRGBO(40, 39, 46, 1)),
                            child: Center(
                              child: ThemeText(
                                  dataStr: state.model?.isOnline == 2
                                      ? 'console_offline'.tr
                                      : 'console_online'.tr,
                                  keyName: "textColor",
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400),
                            ),
                          )
                        ],
                      ),
                    )
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 15.h),
                width: 345.w,
                height: 50.h,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: Colors.white),
                child: Row(
                  children: [
                    SizedBox(width: 25.w),
                    ThemeImagePath(
                      fileName: "console_bind_role.png",
                      imgWidget: 24.r,
                      imgHeight: 20.r,
                    ),
                    SizedBox(width: 10.w),
                    Text(
                      "console_role".tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor),
                    ),
                    const Expanded(child: SizedBox()),
                    Images(
                      path: R.console_right_png,
                      width: 11.r,
                      height: 20.r,
                    ),
                    SizedBox(width: 20.w)
                  ],
                ),
              ).inkWell(() => logic.toRoleConfigPage()),
              Container(
                margin: EdgeInsets.only(top: 10.h),
                width: 345.w,
                height: 50.h,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: Colors.white),
                child: Row(
                  children: [
                    SizedBox(width: 25.w),
                    ThemeImagePath(
                      fileName: "console_bind_language.png",
                      imgWidget: 24.r,
                      imgHeight: 20.r,
                    ),
                    SizedBox(width: 10.w),
                    Text(
                      "console_language_title".tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor),
                    ),
                    const Expanded(child: SizedBox()),
                    Images(
                      path: R.console_right_png,
                      width: 11.r,
                      height: 20.r,
                    ),
                    SizedBox(width: 20.w)
                  ],
                ),
              ).inkWell(() => logic.toLanguagePage()),
              Container(
                margin: EdgeInsets.only(top: 10.h),
                width: 345.w,
                height: 50.h,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: Colors.white),
                child: Row(
                  children: [
                    SizedBox(width: 25.w),
                    ThemeImagePath(
                      fileName: "console_bind_compiled.png",
                      imgWidget: 24.r,
                      imgHeight: 20.r,
                    ),
                    SizedBox(width: 10.w),
                    Text(
                      "console_compiled_title".tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor),
                    ),
                    const Expanded(child: SizedBox()),
                    Images(
                      path: R.console_right_png,
                      width: 11.r,
                      height: 20.r,
                    ),
                    SizedBox(width: 20.w)
                  ],
                ),
              ).inkWell(() => logic.toCompiledPage()),
              Container(
                margin: EdgeInsets.only(top: 10.h),
                width: 345.w,
                height: 50.h,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: Colors.white),
                child: Row(
                  children: [
                    SizedBox(width: 25.w),
                    ThemeImagePath(
                      fileName: "console_sound.png",
                      imgWidget: 24.r,
                      imgHeight: 20.r,
                    ),
                    SizedBox(width: 10.w),
                    Text(
                      "console_sound_title".tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor),
                    ),
                    const Expanded(child: SizedBox()),
                    Images(
                      path: R.console_right_png,
                      width: 11.r,
                      height: 20.r,
                    ),
                    SizedBox(width: 20.w)
                  ],
                ),
              ).inkWell(() => logic.toSoundPage()),
              // Container(
              //   margin: EdgeInsets.only(top: 10.h),
              //   width: 345.w,
              //   height: 50.h,
              //   decoration: BoxDecoration(
              //       borderRadius: BorderRadius.circular(10.r),
              //       color: Colors.white),
              //   child: Row(
              //     children: [
              //       SizedBox(width: 25.w),
              //       ThemeImagePath(
              //         fileName: "console_clear.png",
              //         imgWidget: 24.r,
              //         imgHeight: 20.r,
              //       ),
              //       SizedBox(width: 10.w),
              //       Text(
              //         "console_clear_title".tr,
              //         style: StyleConfig.otherStyle(
              //             color: ColorConfig.searchTextColor),
              //       ),
              //       const Expanded(child: SizedBox()),
              //       Images(
              //         path: R.console_right_png,
              //         width: 11.r,
              //         height: 20.r,
              //       ),
              //       SizedBox(width: 20.w)
              //     ],
              //   ),
              // ).inkWell(() => logic.toClearPage()),
              Container(
                margin: EdgeInsets.only(top: 10.h),
                width: 345.w,
                height: 50.h,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    color: Colors.white),
                child: Row(
                  children: [
                    SizedBox(width: 25.w),
                    ThemeImagePath(
                      fileName: "console_bind_star.png",
                      imgWidget: 24.r,
                      imgHeight: 20.r,
                    ),
                    SizedBox(width: 10.w),
                    Text(
                      "console_update".tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor),
                    ),
                    const Expanded(child: SizedBox()),
                    Images(
                      path: R.console_right_png,
                      width: 11.r,
                      height: 20.r,
                    ),
                    SizedBox(width: 20.w)
                  ],
                ),
              ).inkWell(() => logic.toUpdatePage()),
              const Expanded(child: SizedBox()),
              Container(
                margin: EdgeInsets.only(
                    bottom: ScreenUtil().bottomBarHeight + 30.h),
                width: 283.w,
                height: 48.h,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.r),
                    color: const Color.fromRGBO(40, 39, 46, 1)),
                child: Center(
                  child: ThemeText(
                    dataStr: "console_unbind".tr,
                    keyName: 'textColor',
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ).inkWell(() => logic.unBindAction())
            ],
          );
        },
        initBuilder: () => Column(
          children: [
            CustomShimmer(
              margin: 15.r,
              width: 1.sw,
              height: 127.h,
            ),
            SizedBox(height: 15.h),
            CustomShimmer(
              width: 345.w,
              height: 50.h,
            ),
          ],
        ),
      ),
    );
  }
}
