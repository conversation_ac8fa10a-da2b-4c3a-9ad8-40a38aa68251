import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'register_logic.dart';

class RegisterPage extends StatelessWidget {
  RegisterPage({super.key});

  final logic = Get.put(RegisterLogic());
  final state = Get.find<RegisterLogic>().state;

  ///返回
  Widget _registerWidget() {
    return state.type == 1
        ? Container(
            margin: EdgeInsets.only(
                top: ScreenUtil().statusBarHeight + 15.h, left: 22.w),
            alignment: Alignment.topLeft,
            child: Images(
              path: R.back_png,
              width: 20.w,
              height: 20.h,
              color: Colors.white,
            ).inkWell(() => Get.back()),
          )
        : const SizedBox();
  }

  /// logo和名字
  Widget _titleWidget() {
    Widget temp = const SizedBox();
    switch (state.type) {
      case 1:
        temp = Container(
          margin: EdgeInsets.only(top: 107.h, left: 30.w),
          alignment: Alignment.topLeft,
          child: Text(
            "login_pwd_title".tr,
            style: StyleConfig.otherStyle(
                color: const Color.fromRGBO(69, 47, 10, 1),
                fontWeight: FontWeight.w500,
                fontSize: 22),
          ),
        );
        break;
      case 2:
        temp = Container(
          margin: EdgeInsets.only(top: 107.h, left: 30.w),
          alignment: Alignment.topLeft,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "register_user".tr,
                style: StyleConfig.otherStyle(
                    color: const Color.fromRGBO(69, 47, 10, 1),
                    fontWeight: FontWeight.w500,
                    fontSize: 22),
              ),
              SizedBox(height: 3.h),
              Text(
                "register_user_tips".tr,
                style: StyleConfig.otherStyle(
                    color: const Color.fromRGBO(180, 142, 78, 1), fontSize: 14),
              ),
            ],
          ),
        );
        break;
    }

    return temp;
  }

  /// 账号密码输入框
  Widget _textFiledWidget() {
    return Container(
      margin: EdgeInsets.only(top: 220.h),
      width: 1.sw,
      height: 592.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r), topRight: Radius.circular(20.r)),
        color: Colors.white,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(top: 42.h),
            width: 315.w,
            height: 48.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              color: const Color.fromRGBO(249, 249, 249, 1),
            ),
            child: Row(
              children: [
                Expanded(
                  child: IntlPhoneField(
                    disableLengthCheck: true,
                    showDropdownIcon: false,
                    languageCode: ConfigStore.to.locale.languageCode,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(state.phoneMaxLenght)
                    ],
                    style: StyleConfig.otherStyle(
                        fontSize: 16,
                        color: ColorConfig.searchTextColor,
                        fontWeight: FontWeight.w400),
                    dropdownTextStyle: StyleConfig.otherStyle(
                        fontSize: 16,
                        color: ColorConfig.searchTextColor,
                        fontWeight: FontWeight.w400),
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                            horizontal: 18.w,
                            vertical: Platform.isIOS ? 11.h : 15.h),
                        hintText: 'login_phone_hint_text'.tr,
                        hintStyle: StyleConfig.otherStyle(
                            color: ColorConfig.shopDetailTextColor,
                            fontSize: 14)),
                    initialCountryCode: state.areaCode,
                    pickerDialogStyle: PickerDialogStyle(
                        backgroundColor: Colors.white,
                        searchFieldInputDecoration: InputDecoration(
                            labelText: "login_select_country".tr)),
                    onChanged: (phone) => logic.phoneIsComponse(phone.number),
                    onCountryChanged: (country) {
                      logic.selectCountryAction(country.code, country.dialCode);
                    },
                  ),
                )
                // Container(
                //   padding: EdgeInsets.only(left: 21.w),
                //   width: 68.w,
                //   child: Text(
                //     "+${state.areaCode}",
                //     style: StyleConfig.otherStyle(
                //         color: ColorConfig.searchTextColor,
                //         fontSize: 16,
                //         fontWeight: FontWeight.w400),
                //   ),
                // ).inkWell(() => logic.areaCodeAction()),
                // Container(
                //   width: 1.w,
                //   height: 30.h,
                //   color: const Color.fromRGBO(222, 222, 222, 1),
                // ),
                // Expanded(
                //   child: TextField(
                //     controller: state.phoneController,
                //     //控制键盘的功能键 指enter键，比如此处设置为next时，enter键
                //     //显示的文字内容为 下一步
                //     textInputAction: TextInputAction.next,
                //     style: StyleConfig.otherStyle(
                //         color: ColorConfig.searchTextColor, height: 1),
                //     keyboardType: TextInputType.number,
                //     // 限制输入的 最大长度  TextField右下角没有输入数量的统计字符串
                //     inputFormatters: [
                //       FilteringTextInputFormatter.digitsOnly,
                //       // LengthLimitingTextInputFormatter(11)
                //     ],
                //     decoration: InputDecoration(
                //       border: InputBorder.none,
                //       enabledBorder: InputBorder.none,
                //       focusedBorder: InputBorder.none,
                //       contentPadding: EdgeInsets.symmetric(
                //           horizontal: 18.w, vertical: 12.h),
                //       hintText: '请输入手机号',
                //       hintStyle: StyleConfig.otherStyle(
                //           color: ColorConfig.shopDetailTextColor, height: 1),
                //     ),
                //     onChanged: (value) => logic.isComponse(),
                //   ),
                // )
              ],
            ),
          ),
          SizedBox(height: 16.h),
          Container(
            width: 315.w,
            height: 48.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              color: const Color.fromRGBO(249, 249, 249, 1),
            ),
            child: AppTextField(
              controller: state.passwordController,
              //控制键盘的功能键 指enter键，比如此处设置为next时，enter键
              //显示的文字内容为 下一步
              textInputAction: TextInputAction.next,

              style: StyleConfig.otherStyle(
                  color: ColorConfig.searchTextColor, height: 1, fontSize: 14),
              maxLength: 16,
              obscureText: state.isObscureText,
              decoration: InputDecoration(
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  counterText: '',
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 10.w, vertical: 15.h),
                  hintText: 'register_pwd_hint_text'.tr,
                  hintStyle: StyleConfig.otherStyle(
                      color: ColorConfig.shopDetailTextColor,
                      height: 1,
                      fontSize: 14),
                  suffixIcon: Images(
                    path: state.iconData,
                    scale: 2.8,
                  ).inkWell(() => logic.obscureAction())),
              onChanged: (value) => logic.isComponse(),
            ),
          ),
          SizedBox(height: 16.h),
          Container(
            width: 315.w,
            height: 48.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              color: const Color.fromRGBO(249, 249, 249, 1),
            ),
            child: AppTextField(
              controller: state.password2Controller,
              style: StyleConfig.otherStyle(
                  color: ColorConfig.searchTextColor, height: 1, fontSize: 14),
              maxLength: 16,
              obscureText: state.isObscureText2,
              decoration: InputDecoration(
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  counterText: '',
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 10.w, vertical: 15.h),
                  hintText: 'register_pwd_confirm_hint_text'.tr,
                  hintStyle: StyleConfig.otherStyle(
                      color: ColorConfig.shopDetailTextColor,
                      height: 1,
                      fontSize: 14),
                  suffixIcon: Images(
                    path: state.iconData2,
                    scale: 2.8,
                  ).inkWell(() => logic.obscureAction2())),
              onChanged: (value) => logic.isComponse(),
            ),
          ),
          SizedBox(height: 16.h),
          Container(
            width: 315.w,
            height: 48.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              color: const Color.fromRGBO(249, 249, 249, 1),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: AppTextField(
                    controller: state.verifyController,
                    //控制键盘的功能键 指enter键，比如此处设置为next时，enter键
                    //显示的文字内容为 下一步
                    textInputAction: TextInputAction.next,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor,
                        height: 1,
                        fontSize: 14),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      counterText: '',
                      contentPadding: EdgeInsets.symmetric(
                          horizontal: 10.w, vertical: 12.h),
                      hintText: 'login_verify_hint_text'.tr,
                      hintStyle: StyleConfig.otherStyle(
                          color: ColorConfig.shopDetailTextColor,
                          height: 1,
                          fontSize: 14),
                    ),
                    onChanged: (value) => logic.isComponse(),
                  ),
                ),
                AnimatedPress(
                  seconds: 100,
                  scaleEnd: 0.95,
                  isAuto: false,
                  child: Container(
                    margin: EdgeInsets.only(right: 5.w),
                    width: 80.w,
                    height: 25.h,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(13.r),
                        color: state.isSend
                            ? const Color.fromRGBO(238, 238, 238, 1)
                            : ColorConfig.searchTextColor),
                    child: Center(
                      child: Text(
                        state.verifyStr,
                        style: state.inkWellStyle,
                      ),
                    ),
                  ).inkWell(() => logic.sendPhoneAction()),
                ),
              ],
            ),
          ),
          _protocolWidget(),
          _confirmBtn(),
          _backLoginWidget()
        ],
      ),
    );
  }

  Widget _protocolWidget() {
    Widget temp = const SizedBox();
    switch (state.type) {
      case 2:
        temp = Container(
          margin: EdgeInsets.only(top: 8.h, left: 39.w),
          child: Row(
            children: [
              CheckBoxRounded(
                isChecked: false,
                size: 16.w,
                borderWidth: 1,
                checkedColor: const Color.fromRGBO(255, 232, 174, 1),
                borderColor: ColorConfig.shopDetailTextColor,
                checkedBgColor: ColorConfig.searchTextColor,
                uncheckedBgColor: Colors.transparent,
                onTap: (value) {
                  state.agree = value!;
                },
              ),
              SizedBox(width: 5.w),
              RichText(
                text: TextSpan(
                    text: "login_protocol".tr,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor, fontSize: 12),
                    children: [
                      TextSpan(
                        text: "login_protocol_one".tr,
                        style: StyleConfig.otherStyle(
                            color: const Color.fromRGBO(180, 142, 78, 1),
                            fontSize: 12),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            Get.to(const WebView(),
                                arguments: ConfigStore.to.userAgreement);
                          },
                      ),
                      TextSpan(
                        text: "login_add".tr,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor, fontSize: 12),
                      ),
                      TextSpan(
                        text: "login_protocol_two".tr,
                        style: StyleConfig.otherStyle(
                            color: const Color.fromRGBO(180, 142, 78, 1),
                            fontSize: 12),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            Get.to(const WebView(),
                                arguments: ConfigStore.to.privacyPolicy);
                          },
                      ),
                    ]),
              ),
            ],
          ),
        );
        break;
    }
    return temp;
  }

  Widget _confirmBtn() {
    Widget temp = const SizedBox();
    switch (state.type) {
      case 1:
        temp = Container(
          margin: EdgeInsets.only(top: 38.h),
          width: 283,
          height: 48.h,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24.w),
              color: state.isComfire
                  ? ColorConfig.searchTextColor
                  : const Color.fromRGBO(215, 215, 215, 1)),
          clipBehavior: Clip.hardEdge,
          child: Center(
            child: ThemeText(
              dataStr: "mine_info_birthday_dialog_title".tr,
              keyName: 'textColor',
              fontWeight: FontWeight.w500,
              flag: state.isComfire,
              subColor: ColorConfig.authenticationTextColor,
            ),
          ),
        ).inkWell(() => logic.registerAction());
        break;
      case 2:
        temp = Container(
          margin: EdgeInsets.only(top: 38.h),
          width: 283,
          height: 48.h,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24.w),
              color: state.isComfire
                  ? ColorConfig.searchTextColor
                  : const Color.fromRGBO(215, 215, 215, 1)),
          clipBehavior: Clip.hardEdge,
          child: Center(
            child: ThemeText(
              dataStr: "register_btn".tr,
              keyName: 'textColor',
              fontWeight: FontWeight.w500,
              flag: state.isComfire,
              subColor: ColorConfig.authenticationTextColor,
            ),
          ),
        ).inkWell(() => logic.registerAction());
        break;
    }

    return temp;
  }

  ///返回登录
  Widget _backLoginWidget() {
    return state.type == 1
        ? const SizedBox()
        : Container(
            margin: EdgeInsets.only(top: 18.h),
            child: Text("register_back".tr,
                    textAlign: TextAlign.center,
                    style: StyleConfig.otherStyle(
                        color: const Color.fromRGBO(180, 142, 78, 1),
                        fontSize: 14))
                .inkWell(() => Get.back()),
          );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<RegisterLogic>(
      id: "register_view",
      builder: (_) => Scaffold(
        resizeToAvoidBottomInset: true,
        body: Container(
          constraints: const BoxConstraints.expand(),
          child: Stack(
            children: [
              Container(
                height: 337.h,
                decoration: BoxDecoration(
                  image: DecorationImage(
                      image: AssetImage(
                          state.type == 1 ? R.pwd_bg_png : R.register_bg_png),
                      fit: BoxFit.fill),
                ),
              ),
              _titleWidget(),
              SingleChildScrollView(child: _textFiledWidget()),
              _registerWidget(),
            ],
          ),
        ),
      ),
    );
  }
}
