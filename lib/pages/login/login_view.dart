import 'dart:io';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';
import 'login_logic.dart';

class LoginPage extends BaseCommonView {
  LoginPage({super.key});

  final logic = Get.put(LoginLogic());
  final state = Get.find<LoginLogic>().state;

  ///密码登录/切换验证码登录
  Widget _loginType() {
    return Container(
      margin: EdgeInsets.only(top: 250.h),
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(R.login_image_png),
          fit: BoxFit.fill,
        ),
      ),
      width: 335.w,
      height: 430.h,
      child: Container(
        constraints: const BoxConstraints.expand(),
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(state.loginType == 1
                ? R.login_image_password_png
                : R.login_image_verification_png),
            fit: BoxFit.fill,
          ),
        ),
        child: Column(
          children: [
            /// 密码/验证码切换
            Container(
              margin: EdgeInsets.only(top: 10.h),
              child: Row(
                children: [
                  SizedBox(
                    width: 335.w / 2,
                    height: 44.h,
                    child: Column(
                      children: [
                        Text(
                          'login_pwd'.tr,
                          style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontWeight: state.loginType == 1
                                ? FontWeight.w700
                                : FontWeight.w400,
                          ),
                        ),
                        SizedBox(height: 5.h),
                        Offstage(
                          offstage: state.loginType == 1 ? false : true,
                          child: Container(
                            width: 24.w,
                            height: 4.h,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4.r),
                              color: ColorConfig.searchTextColor,
                            ),
                          ),
                        )
                      ],
                    ),
                  ).inkWell(() => logic.loginTypeAction(1)),
                  SizedBox(
                    width: 335.w / 2,
                    height: 44.h,
                    child: Column(
                      children: [
                        Text(
                          'login_verify'.tr,
                          style: StyleConfig.otherStyle(
                            color: ColorConfig.searchTextColor,
                            fontWeight: state.loginType == 2
                                ? FontWeight.w700
                                : FontWeight.w400,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Offstage(
                          offstage: state.loginType == 2 ? false : true,
                          child: Container(
                            width: 24.w,
                            height: 4.h,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4.r),
                              color: ColorConfig.searchTextColor,
                            ),
                          ),
                        )
                      ],
                    ),
                  ).inkWell(() => logic.loginTypeAction(2)),
                ],
              ),
            ),
            _textFiledWidget(),
            _protocolWidget(),
            _confirmBtn(),
            _registerWidget(),
          ],
        ),
      ),
    );
  }

  /// 账号密码输入框
  Widget _textFiledWidget() {
    return Container(
      margin: EdgeInsets.only(top: 24.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 20.w),
            child: Text(
              'login_phone'.tr,
              style: StyleConfig.blackStyle(fontSize: 14),
            ),
          ),
          Container(
              margin: EdgeInsets.only(top: 12.h),
              padding: EdgeInsets.only(left: 18.w),
              width: 315.w,
              height: 48.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.r),
                color: const Color.fromRGBO(249, 249, 249, 1),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: IntlPhoneField(
                      disableLengthCheck: true,
                      showDropdownIcon: false,
                      initialValue: state.phoneStr,
                      languageCode: ConfigStore.to.locale.languageCode,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(state.phoneMaxLenght)
                      ],
                      style: StyleConfig.otherStyle(
                        fontSize: 16,
                        color: ColorConfig.searchTextColor,
                        fontWeight: FontWeight.w400,
                      ),
                      dropdownTextStyle: StyleConfig.otherStyle(
                        fontSize: 16,
                        color: ColorConfig.searchTextColor,
                        fontWeight: FontWeight.w400,
                      ),
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 18.w,
                              vertical: Platform.isIOS ? 11.h : 15.h),
                          hintText: 'login_phone_hint_text'.tr,
                          hintStyle: StyleConfig.otherStyle(
                            color: ColorConfig.shopDetailTextColor,
                            fontSize: 14,
                          )),
                      initialCountryCode: state.areaCode,
                      pickerDialogStyle: PickerDialogStyle(
                          backgroundColor: Colors.white,
                          searchFieldInputDecoration: InputDecoration(
                              labelText: "login_select_country".tr)),
                      onChanged: (phone) =>
                          logic.phoneInputAction(phone.number),
                      onCountryChanged: (country) {
                        logic.selectCountryAction(
                            country.code, country.dialCode);
                      },
                    ),
                  ),
                  // Container(
                  //   padding: EdgeInsets.only(left: 21.w),
                  //   width: 68.w,
                  //   child: Text(
                  //     "+${state.areaCode}",
                  //     style: StyleConfig.otherStyle(
                  //         color: ColorConfig.searchTextColor,
                  //         fontSize: 16,
                  //         fontWeight: FontWeight.w400),
                  //   ),
                  // ).inkWell(() => logic.areaCodeAction()),
                  // Container(
                  //   width: 1.w,
                  //   height: 30.h,
                  //   color: const Color.fromRGBO(222, 222, 222, 1),
                  // ),
                  // Expanded(
                  //   child: TextField(
                  //     controller: state.phoneController,
                  //     style: StyleConfig.otherStyle(
                  //         color: ColorConfig.searchTextColor, height: 1),
                  //     keyboardType: TextInputType.number,
                  //     inputFormatters: [
                  //       FilteringTextInputFormatter.digitsOnly,
                  //       // LengthLimitingTextInputFormatter(11)
                  //     ],
                  //     decoration: InputDecoration(
                  //         border: InputBorder.none,
                  //         enabledBorder: InputBorder.none,
                  //         focusedBorder: InputBorder.none,
                  //         contentPadding: EdgeInsets.symmetric(
                  //             horizontal: 18.w, vertical: 12.h),
                  //         hintText: '请输入您的账号/手机号码',
                  //         hintStyle: StyleConfig.otherStyle(
                  //             color: ColorConfig.shopDetailTextColor,
                  //             height: 1)),
                  //   ),
                  // )
                ],
              )),
          SizedBox(height: 15.h),

          ///账号密码view
          Offstage(
            offstage: state.loginType == 2 ? true : false,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(left: 20.w),
                  child: Text(
                    'login_pwd_code'.tr,
                    style: StyleConfig.blackStyle(fontSize: 14),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 12.h),
                  width: 315.w,
                  height: 48.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.r),
                    color: const Color.fromRGBO(249, 249, 249, 1),
                  ),
                  child: AppTextField(
                    controller: state.passwordController,
                    style: StyleConfig.otherStyle(
                        color: ColorConfig.searchTextColor),
                    obscureText: state.isObscureText,
                    decoration: InputDecoration(
                        border: InputBorder.none,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        counterText: '',
                        contentPadding: EdgeInsets.symmetric(
                            horizontal: 18.w, vertical: 15.h),
                        hintText: 'login_pwd_hint_text'.tr,
                        hintStyle: StyleConfig.otherStyle(
                            color: ColorConfig.shopDetailTextColor,
                            fontSize: 14),
                        suffixIcon: Images(
                          path: state.iconData,
                          scale: 2.8,
                        ).inkWell(() => logic.obscureAction())),
                  ),
                )
              ],
            ),
          ),

          ///账号验证码view
          Offstage(
            offstage: state.loginType == 2 ? false : true,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(left: 20.w),
                  child: Text(
                    'login_verify_code'.tr,
                    style: StyleConfig.blackStyle(fontSize: 14),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 12.h),
                  width: 315.w,
                  height: 48.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.r),
                    color: const Color.fromRGBO(249, 249, 249, 1),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: AppTextField(
                          controller: state.verifyController,
                          style: StyleConfig.otherStyle(
                              color: ColorConfig.searchTextColor, height: 1),
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            enabledBorder: InputBorder.none,
                            focusedBorder: InputBorder.none,
                            counterText: '',
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 18.w, vertical: 10.h),
                            hintText: 'login_verify_hint_text'.tr,
                            hintStyle: StyleConfig.otherStyle(
                                color: ColorConfig.shopDetailTextColor,
                                height: 1),
                          ),
                        ),
                      ),
                      AnimatedPress(
                        isAuto: false,
                        seconds: 100,
                        scaleEnd: 0.95,
                        child: Container(
                          margin: EdgeInsets.only(right: 5.w),
                          width: 80.w,
                          height: 25.h,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(13.r),
                              color: state.isSend
                                  ? const Color.fromRGBO(238, 238, 238, 1)
                                  : ColorConfig.searchTextColor),
                          child: Center(
                            child: Text(
                              state.verifyStr,
                              style: state.inkWellStyle,
                            ),
                          ),
                        ).inkWell(() => logic.sendPhoneVerifyAction()),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _protocolWidget() {
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      child: Container(
        margin: EdgeInsets.only(top: 5.h),
        width: 1.sw - 60.w,
        child: Row(
          children: [
            CheckBoxRounded(
              isChecked: state.agree,
              size: 16.w,
              borderWidth: 1,
              checkedColor: const Color.fromRGBO(255, 232, 174, 1),
              borderColor: ColorConfig.shopDetailTextColor,
              checkedBgColor: ColorConfig.searchTextColor,
              uncheckedBgColor: Colors.transparent,
              onTap: (value) => logic.agreeAction(value!),
            ),
            SizedBox(width: 5.w),
            RichText(
              text: TextSpan(
                  text: "login_protocol".tr,
                  style: StyleConfig.otherStyle(
                      color: ColorConfig.searchTextColor, fontSize: 12),
                  children: [
                    TextSpan(
                      text: "login_protocol_one".tr,
                      style: StyleConfig.otherStyle(
                          color: const Color.fromRGBO(180, 142, 78, 1),
                          fontSize: 12),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () async {
                          Get.to(const WebView(),
                              arguments: ConfigStore.to.userAgreement);
                        },
                    ),
                    TextSpan(
                      text: "login_add".tr,
                      style: StyleConfig.otherStyle(
                          color: ColorConfig.searchTextColor, fontSize: 12),
                    ),
                    TextSpan(
                      text: "login_protocol_two".tr,
                      style: StyleConfig.otherStyle(
                          color: const Color.fromRGBO(180, 142, 78, 1),
                          fontSize: 12),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () async {
                          Get.to(const WebView(),
                              arguments: ConfigStore.to.privacyPolicy);
                        },
                    ),
                  ]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _confirmBtn() {
    return Container(
      margin: EdgeInsets.only(top: 35.h),
      width: 283.w,
      height: 48.h,
      decoration: BoxDecoration(
        color: const Color.fromRGBO(40, 39, 46, 1),
        borderRadius: BorderRadius.circular(24.w),
      ),
      child: DebounceButton(
        onPressed: () => logic.loginAction(),
        child: Text(
          'login_btn'.tr,
          style: StyleConfig.otherStyle(
              color: const Color.fromRGBO(255, 232, 174, 1),
              fontWeight: FontWeight.w500),
        ),
      ),
    );
  }

  ///注册
  Widget _registerWidget() {
    return Container(
        margin: EdgeInsets.only(top: 18.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'login_forget_pwd'.tr,
              style: StyleConfig.otherStyle(
                  color: const Color.fromRGBO(180, 142, 78, 1), fontSize: 14),
            ).inkWell(() => logic.otherAction(1)),
            Container(
              margin: EdgeInsets.only(left: 8.w, right: 8.w),
              width: 1.w,
              height: 15.h,
              color: const Color.fromRGBO(180, 142, 78, 1),
            ),
            Text(
              'login_register'.tr,
              style: StyleConfig.otherStyle(
                  color: const Color.fromRGBO(180, 142, 78, 1), fontSize: 14),
            ).inkWell(() => logic.otherAction(2)),
          ],
        ));
  }

  @override
  bool get customWidget => true;

  @override
  Widget buildContent() {
    return GetBuilder<LoginLogic>(
      id: "login_view",
      builder: (_) => Scaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: const Color.fromRGBO(247, 247, 247, 1),
        body: Container(
          constraints: const BoxConstraints.expand(),
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                height: 333.h,
                decoration: BoxDecoration(
                  image: DecorationImage(
                      image: AssetImage(ConfigStore.to.getLocaleCode() == "zh"
                          ? R.login_bg_png
                          : R.login_bg_en_png),
                      fit: BoxFit.fill),
                ),
              ),
              SingleChildScrollView(child: _loginType())
            ],
          ),
        ),
      ),
    );
  }
}
