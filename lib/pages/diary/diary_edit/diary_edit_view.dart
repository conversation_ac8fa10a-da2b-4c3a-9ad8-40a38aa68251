import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:getx_xiaopa/base/base.dart';
import 'package:getx_xiaopa/extension/images.dart';
import 'package:getx_xiaopa/extension/widget_extension.dart';
import 'package:getx_xiaopa/network/http_client.dart';
import 'package:getx_xiaopa/pages/diary/diary_edit/widget/image_picker_grid.dart';
import 'package:getx_xiaopa/r.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/utils/utils.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:getx_xiaopa/widget/widget.dart';

import 'diary_edit_logic.dart';
import 'diary_edit_state.dart';

class DiaryEditPage extends BaseCommonView {
  DiaryEditPage({super.key});

  final DiaryEditLogic logic = Get.put(DiaryEditLogic());
  final DiaryEditState state = Get.find<DiaryEditLogic>().state;

  @override
  bool get customWidget => true;

  @override
  Widget buildContent() {
    return GetBuilder<DiaryEditLogic>(
      id: "diary_edit_view",
      builder: (_) {
        return Scaffold(
          resizeToAvoidBottomInset: true,
          backgroundColor: Colors.white,
          appBar: AppBar(
            elevation: 0,
            scrolledUnderElevation: 0,
            backgroundColor: Colors.white,
            centerTitle: true,
            title: Text(
              "diary_edit_title".tr,
              style: StyleConfig.blackStyle(
                  fontSize: 18, fontWeight: FontWeight.w500),
            ),
            leadingWidth: 35.w,
            leading: Padding(
              padding: EdgeInsets.only(left: 15.w),
              child: Images(
                path: R.back_png,
                width: 20.w,
                height: 20.h,
                scale: 2.8,
              ).inkWell(() => Get.back()),
            ),
            actions: [
              state.isCompleted
                  ? const SizedBox()
                  : Padding(
                      padding: EdgeInsets.only(right: 27.w),
                      child: Text(
                        "diary_edit_btn".tr,
                        style: StyleConfig.otherStyle(
                            color: Get.find<ThemeColorController>()
                                .getColor('iconColor'),
                            fontSize: 16),
                      ),
                    ).inkWell(() => logic.editAction())
            ],
          ),
          body: createCommonView(
            logic,
            (_) {
              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Align(
                      alignment: Alignment.center,
                      child: Text(
                        state.diaryDate,
                        style: StyleConfig.otherStyle(
                            color: ColorConfig.authenticationTextColor),
                      ),
                    ),
                    SizedBox(height: 10.h),
                    SwiperWidget(
                      imagesList: state.diaryItemsModel.picture!.isNotEmpty
                          ? state.diaryItemsModel.picture!.split(",")
                          : [state.diaryMoodImage],
                      width: 1.sw,
                      height: 1.sw,
                      type: 1,
                    ),
                    SizedBox(height: 20.h),
                    Container(
                      margin: EdgeInsets.only(left: 30.w),
                      child: Wrap(
                        spacing: 10.w,
                        runSpacing: 10.h,
                        children: List.generate(
                            state.isEdit
                                ? state.editDiaryFeeling.split(",").length
                                : state.signTags.split(",").length, (index) {
                          return _buildSign(state.isEdit
                              ? state.editDiaryFeeling.split(",")[index]
                              : state.signTags.split(",")[index]);
                        }),
                      ),
                    ),
                    SizedBox(height: 12.h),
                    Container(
                      margin: EdgeInsets.only(left: 38.w, right: 15.w),
                      child: Wrap(
                        spacing: 10.w,
                        runSpacing: 10.h,
                        children: List.generate(
                            state.isEdit
                                ? state.editDiaryThing.split(",").length
                                : state.eventTags.split(",").length, (index) {
                          return _buildEvents(state.isEdit
                              ? state.editDiaryThing.split(",")[index]
                              : state.eventTags.split(",")[index]);
                        }),
                      ),
                    ),
                    SizedBox(height: 24.h),
                    state.isEdit
                        ? Container(
                            margin: EdgeInsets.only(left: 38.w, right: 38.w),
                            padding: EdgeInsets.only(
                                top: 9.h, left: 18.w, right: 18.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10.r),
                              color: const Color.fromRGBO(249, 249, 249, 1),
                            ),
                            child: Column(
                              children: [
                                AppTextField(
                                  controller: state.editDiaryContentController,
                                  style: StyleConfig.otherStyle(
                                      color: ColorConfig.searchTextColor,
                                      fontSize: 14),
                                  keyboardType: TextInputType.text,
                                  maxLines: null,
                                  maxLength: state.editDiaryContentMaxLength,
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    enabledBorder: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    counterText: '',
                                    hintText: '',
                                    hintStyle: StyleConfig.otherStyle(
                                        color: ColorConfig.searchTextColor,
                                        fontSize: 14),
                                  ),
                                  onChanged: (value) =>
                                      logic.changeContentAction(value),
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Text(
                                      "${state.editDiaryContentLength}",
                                      style: StyleConfig.otherStyle(
                                          color: ColorConfig
                                              .authenticationTextColor,
                                          fontSize: 12),
                                    ),
                                    Text(
                                      "/${state.editDiaryContentMaxLength}",
                                      style: StyleConfig.otherStyle(
                                          color:
                                              ColorConfig.shopDetailTextColor,
                                          fontSize: 12),
                                    )
                                  ],
                                ),
                                SizedBox(height: 10.h),
                              ],
                            ),
                          )
                        : Container(
                            margin: EdgeInsets.only(left: 38.w, right: 38.w),
                            child: Text(
                              state.diaryItemsModel.content ?? "",
                              textAlign: TextAlign.start,
                              style: StyleConfig.otherStyle(
                                  color: ColorConfig.searchTextColor,
                                  fontSize: 14),
                            ),
                          ),
                    SizedBox(height: 9.h),
                    ImagePickerGrid(
                      images: state.diaryMedia,
                      onAddImage: () => logic.pickerImageAction(),
                      onRemoveImage: (int index) =>
                          logic.pickerImageRemove(index),
                    ),
                    if (state.isCompleted) ...{
                      SizedBox(height: 30.h),
                      Container(
                        margin: EdgeInsets.only(
                            left: 46.w, right: 46.w, bottom: 20.h),
                        height: 48.h,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.r),
                            color: const Color.fromRGBO(40, 39, 46, 1)),
                        child: Center(
                          child: ThemeText(
                            dataStr: "diary_edit_btn_complete".tr,
                            keyName: 'textColor',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ).inkWell(() => logic.changeAction()),
                    },
                    SizedBox(height: 20.h)
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildSign(String text) {
    return ThemeContainer(
      tWidget: 64.w,
      tHeight: 25.h,
      radius: 17.r,
      child: Center(
        child: ThemeText(
          dataStr: text,
          keyName: 'diaryRecord',
          fontSize: ConfigStore.to.getLocaleCode() == "zh" ? 12 : 10,
        ),
      ),
    );
  }

  Widget _buildEvents(String str) {
    return Images(
      path: fileUrl(CommonStore.to.getDiaryThingImage(str)),
      width: 40.r,
      height: 40.r,
    );
  }
}
