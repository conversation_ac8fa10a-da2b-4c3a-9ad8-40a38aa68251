// ignore_for_file: depend_on_referenced_packages

import 'package:dio/dio.dart';
import 'package:getx_xiaopa/pages/ai_chat/model/start_brain_model.dart';
import 'package:getx_xiaopa/pages/console/console_schedule/model/schedule_model_result.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/model/voice_list_model.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_sound/sound_exchange/model/voice_buy_model.dart';
import 'package:getx_xiaopa/pages/console/console_setting/console_update/model/version_device.dart';
import 'package:getx_xiaopa/pages/console/model/device_items_result.dart';
import 'package:getx_xiaopa/pages/diary/model/diary_feeling_model_result.dart.dart';
import 'package:getx_xiaopa/pages/diary/model/diary_items_result.dart';
import 'package:getx_xiaopa/pages/diary/model/diary_mood_items_result.dart';
import 'package:getx_xiaopa/pages/diary/model/diary_statics_items_result.dart';
import 'package:getx_xiaopa/pages/diary/model/diary_thing_items_result.dart';
import 'package:getx_xiaopa/pages/login/model/user_info_model.dart';
import 'package:getx_xiaopa/pages/mine/model/task_skin_user_result.dart';
import 'package:getx_xiaopa/pages/mine/model/version_model.dart';
import 'package:getx_xiaopa/pages/period/index.dart';
import 'package:getx_xiaopa/pages/recharge/index.dart';
import 'package:getx_xiaopa/pages/recharge/record/model/account_flow_items_model.dart';

import 'package:getx_xiaopa/pages/register/model/image_verify_model.dart';
import 'package:getx_xiaopa/pages/task/model/task_item_result.dart';
import 'package:getx_xiaopa/pages/task/model/task_remind_result.dart';
import 'package:getx_xiaopa/pages/task/model/task_skin_result.dart';
import 'package:getx_xiaopa/pages/task/model/task_statics_result.dart';
import 'package:getx_xiaopa/pages/task/model/task_target_result.dart';
import 'package:getx_xiaopa/pages/task/model/task_type_result.dart';
import 'package:getx_xiaopa/storage/model/base_ad_result.dart';
import 'package:getx_xiaopa/storage/model/brain_detail_model.dart';
import 'package:getx_xiaopa/storage/model/setu_public_result.dart';
import 'package:getx_xiaopa/storage/storage.dart';
import 'package:getx_xiaopa/values/values.dart';
import 'package:retrofit/retrofit.dart';

import 'dio_util.dart';
import 'model/result.dart';

part 'http_client.g.dart';

///是否生产环境
bool isProduction =
    SharedPreferencesUtil.to.getBool(STORAGE_IS_PRODUCTION, defaultBool: true);

//http://test.zerzhi.com
String domain =
    isProduction ? "dev.aitoy.api.gdhuoxing.com" : "xiaopa.api.marspt.cn";

String baseUrl = isProduction ? "https://$domain" : "https://$domain";

// String domain = '192.168.2.210:8815';
// String baseUrl = "http://$domain";

String socketUrl = "ws://$domain/client/bot/device/appsocket";

String ossBaseUrl = "http://$domain";

String fileUrl(String path) {
  if (path.startsWith("/") || path.startsWith("assets")) {
    return path;
  }
  if (path.startsWith("http")) {
    return path;
  }
  if (path.startsWith("oss://")) {
    return '$ossBaseUrl${path.replaceFirst("oss://", "/")}';
  }
  return "$baseUrl/$path";
}

var http = Http(DioUtil.getInstance().createDioInstance(
  server: () {
    return baseUrl;
  },
  token: () {
    return '';
  },
));

@RestApi()
abstract class Http {
  factory Http(Dio dio, {String baseUrl}) = _Http;

  ///======================配置项===========================
  @POST('/client/setup/setup/public')
  Future<Result<SetuPublicResult>> setupPublic(
      @Body() Map<String, dynamic> map);

  ///======================图片广告管理========================
  ///获取图片
  @POST('/client/base/ad/list')
  Future<Result<BaseAdResult>> getBaseAd(@Body() Map<String, dynamic> map);

  ///=======================登录注册=======================
  ///获取图像验证码
  @POST('/client/base/captcha/graphicGet')
  Future<Result<ImageVerifyModel>> getImageVerify(
      @Body() Map<String, dynamic> map);

  ///验证图片验证码
  @POST('/client/base/captcha/graphicCheck')
  Future<Result> checkImageVerify(@Body() Map<String, dynamic> map);

  ///发送手机验证码(不带验证码参数)
  @POST('/client/base/sms/send')
  Future<Result> sendPhoneVerify(@Body() Map<String, dynamic> map);

  ///用户注册
  @POST('/client/user/user/register')
  Future<Result> register(@Body() Map<String, dynamic> map);

  ///注册登录一体
  @POST('/client/user/user/loginRegister')
  Future<Result<UserInfoModel>> loginRegiter(@Body() Map<String, dynamic> map);

  ///登录
  @POST('/client/user/user/login')
  Future<Result<UserInfoModel>> login(@Body() Map<String, dynamic> map);

  ///版本检测
  @POST("/client/setup/version/latest")
  Future<Result<VersionModel>> getVersion(@Body() Map<String, dynamic> map);

  ///=============获取公共数字人列表====================
  @POST("/client/bot/bot/detail")
  Future<Result<BrainDetailModel>> getBrainDetail(
      @Body() Map<String, dynamic> map);

  ///=======================个人中心===============================
  ///用户信息
  @POST('/client/user/user/query')
  Future<Result<UserInfoModel>> getUserInfo(@Body() Map<String, dynamic> map);

  ///用户信息修改(修改头像和昵称)
  @POST('/client/user/user/update')
  Future<Result> updateUserInfo(@Body() Map<String, dynamic> map);

  ///修改登录密码
  @POST('/client/user/user/retrieve')
  Future<Result> retrieveUserInfo(@Body() Map<String, dynamic> map);

  ///重置密码
  @POST('/client/user/user/reset')
  Future<Result> resetUserInfo(@Body() Map<String, dynamic> map);

  ///用户退出登录
  @POST('/client/user/user/logout')
  Future<Result> loginOut(@Body() Map<String, dynamic> map);

  ///用户注销
  @POST('/client/user/user/cancel')
  Future<Result> cancelUserInfo(@Body() Map<String, dynamic> map);

  ///获取已解锁皮肤列表
  @POST('/client/bot/skin/user/items')
  Future<Result<TaskSkinUserResult>> taskShinUserItems(
      @Body() Map<String, dynamic> map);

  ///选择皮肤
  @POST('/client/bot/skin/user/update')
  Future<Result> taskShinUserUpdate(@Body() Map<String, dynamic> map);

  ///反馈和建议
  @POST('/client/bot/feedback/create')
  Future<Result> feedbackCreate(@Body() Map<String, dynamic> map);

  ///======================充值相关==============================
  ///充值套餐
  @POST('/client/account/package/list')
  Future<Result<PackageModelResult>> accountPackageList(
      @Body() Map<String, dynamic> map);

  ///支付方式
  @POST('/client/account/payment/list')
  Future<Result<PaymentModelResult>> accountPaymentList(
      @Body() Map<String, dynamic> map);

  ///充值下单
  @POST('/client/order/order/create')
  Future<Result<CreateOrder>> orderCreate(@Body() Map<String, dynamic> map);

  ///papa币流水
  @POST('/client/account/flow/items')
  Future<Result<AccoundFlowItemsModelResult>> accountFlowList(
      @Body() Map<String, dynamic> map);

  ///账户查询
  @POST('/client/account/account/items')
  Future<Result<AccoundItemModelResilt>> accountQueryItems(
      @Body() Map<String, dynamic> map);

  ///apple订单上报
  @POST('/client/order/order/apple')
  Future<Result> orderApple(@Body() Map<String, dynamic> map);

  ///======================火山RTC================================
  ///启动智能体
  @POST('/client/bot/bot/start')
  Future<Result<StartBrainModel>> startBrain(@Body() Map<String, dynamic> map);

  ///打断智能体
  @POST('/client/bot/bot/interrupt')
  Future<Result> interruptBrain(@Body() Map<String, dynamic> map);

  ///关闭智能体
  @POST('/client/bot/bot/stop')
  Future<Result> stopBrain(@Body() Map<String, dynamic> map);

  ///====================硬件相关==================================
  ///绑定硬件
  @POST('/client/bot/device/bind')
  Future<Result> bindDevice(@Body() Map<String, dynamic> map);

  ///设备详情
  @POST('/client/bot/device/detail')
  Future<Result<DeviceItemsModel>> deviceDetail(
      @Body() Map<String, dynamic> map);

  ///解绑硬件
  @POST('/client/bot/device/unbind')
  Future<Result> unbindDevice(@Body() Map<String, dynamic> map);

  ///检测设备是否被绑定
  @POST('/client/bot/device/check')
  Future<Result> checkDevice(@Body() Map<String, dynamic> map);

  ///设置硬件
  @POST('/client/bot/device/setup')
  Future<Result> setUpDevice(@Body() Map<String, dynamic> map);

  ///已绑定的硬件
  @POST('/client/bot/device/items')
  Future<Result<DeviceItemsResult>> itemsDevice(
      @Body() Map<String, dynamic> map);

  ///硬件版本号
  @POST('/client/setup/version/latest')
  Future<Result<VersionDeviceModel>> versionDevice(
      @Body() Map<String, dynamic> map);

  ///互动偏好
  @POST('/client/bot/device/update')
  Future<Result> updateDevicePreference(@Body() Map<String, dynamic> map);

  ///清空记忆
  @POST('/client/bot/bot/reset')
  Future<Result> clearDeviceMemory(@Body() Map<String, dynamic> map);

  ///=====================声音复刻==============================
  ///papa币兑换声音包
  @POST('/client/bot/voice/buy')
  Future<Result<VoiceBuyModel>> voiceBuy(@Body() Map<String, dynamic> map);

  ///音色激活码激活使用
  @POST('/client/bot/sn/active')
  Future<Result<VoiceBuyModel>> botSnActivate(@Body() Map<String, dynamic> map);

  ///清空自定义音色默认使用
  @POST('/client/bot/voice/reset')
  Future<Result> voiceReset(@Body() Map<String, dynamic> map);

  ///音色训练
  @POST('/client/bot/voice/train')
  Future<Result> voiceTrain(@Body() Map<String, dynamic> map);

  ///音色使用
  @POST('/client/bot/voice/update')
  Future<Result> voiceUpdate(@Body() Map<String, dynamic> map);

  ///公用音色
  @POST('/client/bot/voice/public')
  Future<Result<VoiceListModelResult>> voicePublicList(
      @Body() Map<String, dynamic> map);

  ///我的音色
  @POST('/client/bot/voice/items')
  Future<Result<VoiceListModelResult>> voiceMyItems(
      @Body() Map<String, dynamic> map);

  ///=====================喝水任务===============================
  ///任务类型列表
  @POST('/client/bot/task/type/list')
  Future<Result<TaskTypeResult>> taskTypeItems(
      @Body() Map<String, dynamic> map);

  ///任务提醒
  @POST('/client/bot/task/remind/items')
  Future<Result<TaskRemindResult>> taskRemindItems(
      @Body() Map<String, dynamic> map);

  ///任务目标
  @POST('/client/bot/task/target/items')
  Future<Result<TaskTargetResult>> taskTargetItems(
      @Body() Map<String, dynamic> map);

  ///任务目标修改
  @POST('/client/bot/task/target/update')
  Future<Result<TaskTargetModel>> taskTargetUpdate(
      @Body() Map<String, dynamic> map);

  ///任务提醒修改
  @POST('/client/bot/task/remind/update')
  Future<Result<TaskRemindModel>> taskRemindUpdate(
      @Body() Map<String, dynamic> map);

  ///任务列表
  @POST('/client/bot/task/items')
  Future<Result<TaskItemResult>> taskItems(@Body() Map<String, dynamic> map);

  ///任务创建
  @POST('/client/bot/task/create')
  Future<Result<TaskItemModel>> taskCreate(@Body() Map<String, dynamic> map);

  ///任务统计列表
  @POST('/client/bot/statics/task/items')
  Future<Result<TaskStaticsResult>> taskStatisticItems(
      @Body() Map<String, dynamic> map);

  ///任务统计(当天、当周、当月)
  @POST('/client/bot/statics/task/current')
  Future<Result> taskStatisticCurrent(@Body() Map<String, dynamic> map);

  ///获取成就列表
  @POST('/client/bot/skin/list')
  Future<Result<TaskSkinResult>> taskShinItems(
      @Body() Map<String, dynamic> map);

  /// ==================心情记录===============================
  /// 心情日记记录
  @POST('/client/bot/diary/items')
  Future<Result<DiaryItemsResult>> diaryItems(@Body() Map<String, dynamic> map);

  /// 心情统计列表
  @POST('/client/bot/statics/diary/items')
  Future<Result<DiaryStaticsItemsResult>> diaryStatisticItems(
      @Body() Map<String, dynamic> map);

  ///心情统计列表(当天、当周、当月)
  @POST('/client/bot/statics/diary/current')
  Future<Result> diaryStatisticCurrent(@Body() Map<String, dynamic> map);

  ///心情列表
  @POST('/client/bot/diary/mood/list')
  Future<Result<DiaryMoodItemsResult>> diaryMoodItems(
      @Body() Map<String, dynamic> map);

  ///事情列表
  @POST('/client/bot/diary/thing/list')
  Future<Result<DiaryThingItemsResult>> diaryThingItems(
      @Body() Map<String, dynamic> map);

  ///Feeling列表
  @POST('/client/bot/diary/feeling/list')
  Future<Result<DiaryFeelingModelResult>> diaryFeelingItems(
      @Body() Map<String, dynamic> map);

  ///心情日记修改
  @POST('/client/bot/diary/update')
  Future<Result<DiaryItemsModel>> diaryUpdate(@Body() Map<String, dynamic> map);

  ///心情日记删除
  @POST('/client/bot/diary/remove')
  Future<Result> diaryDelete(@Body() Map<String, dynamic> map);

  ///心情日记查询
  @POST('/client/bot/diary/query')
  Future<Result<DiaryItemsModel>> diaryQuery(@Body() Map<String, dynamic> map);

  ///======================日程=========================
  ///创建日程
  @POST('/client/bot/bot/schedule/create')
  Future<Result> createSchedule(@Body() Map<String, dynamic> map);

  ///删除日程
  @POST('/client/bot/bot/schedule/remove')
  Future<Result> rmoveSchedule(@Body() Map<String, dynamic> map);

  ///日程列表
  @POST('/client/bot/bot/schedule/items')
  Future<Result<ScheduleModelResult>> itemsSchedule(
      @Body() Map<String, dynamic> map);

  ///上传崩溃日志
  @POST('/client/log/exception/create')
  Future<Result> uploadCrashLog(@Body() Map<String, dynamic> map);

  ///========================经期==========================
  ///提交经期信息
  @POST('/client/bot/menstrual/submit/create')
  Future<Result> periodSubmitCreate(@Body() Map<String, dynamic> map);

  ///获取经期信息
  @POST('/client/bot/menstrual/submit/info')
  Future<Result<PeriodSubmitModel>> periodSubmitInfo(
      @Body() Map<String, dynamic> map);

  ///更新经期信息
  @POST('/client/bot/menstrual/submit/update')
  Future<Result> periodSubmitUpdate(@Body() Map<String, dynamic> map);

  ///第一次创建经期记录
  @POST('/client/bot/menstrual/cycle/firstCreate')
  Future<Result> periodCycleCreate(@Body() Map<String, dynamic> map);

  ///获取当前周期各时期信息
  @POST('/client/bot/menstrual/cycle/info')
  Future<Result<PeriodCycleInfoModel>> periodCycleInfo(
      @Body() Map<String, dynamic> map);

  ///记录周期
  @POST('/client/bot/menstrual/cycle/record')
  Future<Result> periodCycleRecord(@Body() Map<String, dynamic> map);

  ///周期列表
  @POST('/client/bot/menstrual/cycle/items')
  Future<Result<PeriodCycleItemModelResult>> periodCycleItem(
      @Body() Map<String, dynamic> map);

  ///批量修改保存周期和经期
  @POST('/client/bot/menstrual/cycle/batchSave')
  Future<Result> periodCycleBatchSave(@Body() Map<String, dynamic> map);

  ///记录月经情况
  @POST('/client/bot/menstrual/record/create')
  Future<Result> periodRecordCreate(@Body() Map<String, dynamic> map);

  ///月经情况列表
  @POST('/client/bot/menstrual/period/items')
  Future<Result<PeriodItemModelResult>> periodItem(
      @Body() Map<String, dynamic> map);
}
