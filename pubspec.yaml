name: getx_xiaopa
description: "小耙App"

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

#iOS
# version: 1.6.1+14
#Android
version: 1.6.1+14

environment:
  sdk: '>=3.4.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  # 状态管理
  get: ^4.6.6
  # 网络请求
  dio: ^5.4.2+1
  # 数据持久化
  shared_preferences: ^2.2.2
  # 图片缓存
  cached_network_image: ^3.3.1
  # 屏幕适配
  flutter_screenutil: ^5.9.0
  # toast
  fluttertoast: ^8.2.4
  flutter_easyloading: ^3.0.5
  # 应用图标，命令：flutter pub run flutter_launcher_icons:main
  flutter_launcher_icons: ^0.13.1
  # 设备信息
  device_info_plus: ^10.1.0
  # 项目信息
  package_info_plus: ^8.0.2
  # 动态权限
  permission_handler: ^11.3.1
  # 上拉刷新
  pull_to_refresh: ^2.0.0
  # 日志
  logger: ^2.2.0
  # 加载前占位符
  shimmer: ^3.0.0
  # 字体自动缩放
  auto_size_text: ^3.0.0
  # 加载动画
  loading_animation_widget: ^1.2.1
  # 格子流水布局
  flutter_staggered_grid_view: ^0.7.0
  # 图片预览
  photo_view: ^0.15.0
  # 保存图片
  image_gallery_saver: ^2.0.3
  # 图片选择器(多选)
  wechat_assets_picker: ^9.5.0
  # 文件处理
  path_provider: ^2.1.3
  # 下载器
  flutter_downloader: ^1.11.8
  # 验证码组件
  pin_code_fields: ^8.0.1
  # 图形(不要升级版本，不然有语法错误
  fl_chart: ^0.69.2
  # 日历控件
  table_calendar: ^3.1.3
  # 文件夹操作
  open_file: ^3.5.10
  file_picker: ^10.1.9

  animations: ^2.0.7 # 或使用最新版本

  # 轮播图
  flutter_swiper_null_safety_flutter3: ^4.0.3

  # 时间处理
  intl: ^0.19.0

  #视频播放器
  # fijkplayer_update: ^1.1.1
  #音频播放器
  just_audio: ^0.9.37

  #音频录制
  record: ^6.0.0

  webview_flutter: ^4.8.0
  # 进度条控件
  percent_indicator: ^4.2.3

  # 图像裁剪器
  image_cropper: ^9.1.0
  # 钱数值处理
  decimal: ^2.3.3

  # websocket
  web_socket_channel: ^3.0.3

  url_launcher: ^6.3.0
  #火山RTC
  volc_engine_rtc: ^3.58.1

  # 二维码
  qr_flutter: ^4.1.0

  # 左滑出现删除按钮插件
  flutter_slidable: ^4.0.0

  # 设置屏幕常亮插件
  wakelock_plus: ^1.3.2

  # 传感器
  sensors_plus: ^6.1.1

  # extended_image: ^10.0.1
  # 内购 iOS和谷歌
  in_app_purchase: ^3.2.3

  # 重启app iOS要开启通知，关闭app后点击通知打开app。不开启不会重启
  restart_app: ^1.3.2
  
  # 极光推送
  # jpush_flutter: ^3.3.4

  # 允许滚动到列表中特定项目
  scrollable_positioned_list: ^0.3.8

  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.6

dev_dependencies:
  flutter_test:
    sdk: flutter
  # 接口生成，命令：flutter pub run build_runner build
  retrofit_generator: ^8.1.0
  json_serializable: ^6.7.1
  build_runner: ^2.4.8

  flutter_lints: ^3.0.0

flutter_launcher_icons:
  android: "ic_launcher"
  ios: true
  image_path_android: "assets/icons/android.png"
  image_path_ios: "assets/icons/ios.png"
  remove_alpha_ios: true
  adaptive_icon_background: "assets/icons/ic_launcher_background.png"
  adaptive_icon_foreground: "assets/icons/ic_launcher_foreground.png"

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/images/default/
    - assets/images/pink/
    - assets/audio/
    - assets/emojis/
    - assets/flags/

  fonts:
    - family: Alibaba
      fonts:
        - asset: assets/fonts/AlibabaPuHuiTi.ttf

    - family: AlibabaPuHuiTi
      fonts:
        - asset: assets/fonts/AlibabaPuHuiTi-Regular.ttf
