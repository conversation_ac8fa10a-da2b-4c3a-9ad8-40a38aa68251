// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		2F13C55D2DFBC85800E254FC /* XiaoHongShuOpenSDK.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2F13C55C2DFBC85800E254FC /* XiaoHongShuOpenSDK.xcframework */; };
		2F13C55F2DFBC8BB00E254FC /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 2F13C55E2DFBC8B100E254FC /* libz.tbd */; };
		2F13C5602DFBC8C400E254FC /* libsqlite3.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = C962DC752C87147D004E3CF1 /* libsqlite3.tbd */; };
		2F13C5642DFBD47F00E254FC /* XhsUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 2F13C5632DFBD47F00E254FC /* XhsUtil.m */; };
		2F36E21B2E331A6F00E91FEA /* ReceiptHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F36E21A2E331A6F00E91FEA /* ReceiptHelper.swift */; };
		2F6ACDDC2E2DD68C00575DD4 /* WechatOpenSDK-XCFramework.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2F6ACDDB2E2DD68C00575DD4 /* WechatOpenSDK-XCFramework.xcframework */; };
		2F849CE52E4D8B1900D40133 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 2F849CE32E4D8B1900D40133 /* InfoPlist.strings */; };
		2F8EBCFB2E3B021800CB8157 /* WxStream.m in Sources */ = {isa = PBXBuildFile; fileRef = 2F8EBCFA2E3B021800CB8157 /* WxStream.m */; };
		2FC44EAE2E0155D4005960EC /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2FC44EAD2E0155D4005960EC /* CoreGraphics.framework */; };
		2FC44EB02E0155DE005960EC /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2FC44EAF2E0155DE005960EC /* Security.framework */; };
		2FC44EB22E0155E7005960EC /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2FC44EB12E0155E7005960EC /* WebKit.framework */; };
		2FC44EB62E0156F1005960EC /* WxUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = 2FC44EB52E0156F1005960EC /* WxUtil.m */; };
		2FD717E22E12765500955D8C /* XhsStream.m in Sources */ = {isa = PBXBuildFile; fileRef = 2FD717E12E12765500955D8C /* XhsStream.m */; };
		2FD81F602E1CBD5C000EEFB2 /* CrashHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 2FD81F5F2E1CBD5C000EEFB2 /* CrashHandler.m */; };
		331C808B294A63AB00263BE5 /* RunnerTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 331C807B294A618700263BE5 /* RunnerTests.swift */; };
		3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		59EE6B57F723E270E54DA109 /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 12792590A185A705FC447430 /* Pods_Runner.framework */; };
		917E44F9C1CC8A647589E905 /* Pods_RunnerTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9DF2653377DC32A3EDCF6155 /* Pods_RunnerTests.framework */; };
		97C146FC1CF9000F007C117D /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		C94179BF2DA8C90400B1937B /* EspBlufiStream.m in Sources */ = {isa = PBXBuildFile; fileRef = C94179BE2DA8C90400B1937B /* EspBlufiStream.m */; };
		C94179C22DA8C91800B1937B /* EspBlufiUtil.m in Sources */ = {isa = PBXBuildFile; fileRef = C94179C12DA8C91800B1937B /* EspBlufiUtil.m */; };
		C94179CA2DA8F12A00B1937B /* ESPPeripheral.m in Sources */ = {isa = PBXBuildFile; fileRef = C94179C92DA8F12A00B1937B /* ESPPeripheral.m */; };
		C94179CD2DA8FA3100B1937B /* ESPDataConversion.m in Sources */ = {isa = PBXBuildFile; fileRef = C94179CC2DA8FA3100B1937B /* ESPDataConversion.m */; };
		C9417A5E2DA9212000B1937B /* BlufiConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = C94179D82DA9212000B1937B /* BlufiConstants.m */; };
		C9417A5F2DA9212000B1937B /* BlufiVersionResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = C94179DB2DA9212000B1937B /* BlufiVersionResponse.m */; };
		C9417A602DA9212000B1937B /* BlufiSecurity.m in Sources */ = {isa = PBXBuildFile; fileRef = C9417A542DA9212000B1937B /* BlufiSecurity.m */; };
		C9417A612DA9212000B1937B /* BlufiNotifyData.m in Sources */ = {isa = PBXBuildFile; fileRef = C94179D42DA9212000B1937B /* BlufiNotifyData.m */; };
		C9417A622DA9212000B1937B /* BlufiStatusResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = C94179DA2DA9212000B1937B /* BlufiStatusResponse.m */; };
		C9417A632DA9212000B1937B /* BlufiFrameCtrlData.m in Sources */ = {isa = PBXBuildFile; fileRef = C94179D22DA9212000B1937B /* BlufiFrameCtrlData.m */; };
		C9417A642DA9212000B1937B /* BlufiClient.m in Sources */ = {isa = PBXBuildFile; fileRef = C94179D62DA9212000B1937B /* BlufiClient.m */; };
		C9417A652DA9212000B1937B /* BlufiDH.m in Sources */ = {isa = PBXBuildFile; fileRef = C9417A522DA9212000B1937B /* BlufiDH.m */; };
		C9417A662DA9212000B1937B /* BlufiScanResponse.m in Sources */ = {isa = PBXBuildFile; fileRef = C94179D92DA9212000B1937B /* BlufiScanResponse.m */; };
		C9417A672DA9212000B1937B /* BlufiConfigureParams.m in Sources */ = {isa = PBXBuildFile; fileRef = C94179D72DA9212000B1937B /* BlufiConfigureParams.m */; };
		C9417A682DA9212000B1937B /* LICENSE in Resources */ = {isa = PBXBuildFile; fileRef = C9417A4B2DA9212000B1937B /* LICENSE */; };
		C962DC512C856764004E3CF1 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = C962DC502C856764004E3CF1 /* AppDelegate.m */; };
		C962DC532C856808004E3CF1 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = C962DC522C856808004E3CF1 /* main.m */; };
		C962DC612C85A2C1004E3CF1 /* ByteTtsStream.m in Sources */ = {isa = PBXBuildFile; fileRef = C962DC5F2C85A2C1004E3CF1 /* ByteTtsStream.m */; };
		C962DC642C85A3B8004E3CF1 /* ByteTtsPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = C962DC632C85A3B8004E3CF1 /* ByteTtsPlugin.m */; };
		C962DC672C85A980004E3CF1 /* RegisterPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = C962DC662C85A980004E3CF1 /* RegisterPlugin.m */; };
		C962DC6A2C85B2BB004E3CF1 /* ByteAsrPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = C962DC692C85B2BB004E3CF1 /* ByteAsrPlugin.m */; };
		C962DC6D2C85B3DF004E3CF1 /* ByteAsrStream.m in Sources */ = {isa = PBXBuildFile; fileRef = C962DC6C2C85B3DF004E3CF1 /* ByteAsrStream.m */; };
		C962DC702C85B543004E3CF1 /* ByteVolumeStream.m in Sources */ = {isa = PBXBuildFile; fileRef = C962DC6F2C85B543004E3CF1 /* ByteVolumeStream.m */; };
		C962DC742C85C2F1004E3CF1 /* StreamRecorder.m in Sources */ = {isa = PBXBuildFile; fileRef = C962DC722C85C2F1004E3CF1 /* StreamRecorder.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		331C8085294A63A400263BE5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 97C146E61CF9000F007C117D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 97C146ED1CF9000F007C117D;
			remoteInfo = Runner;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0E8445970BAC87AF95E1DE26 /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		12792590A185A705FC447430 /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeneratedPluginRegistrant.h; sourceTree = "<group>"; };
		1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GeneratedPluginRegistrant.m; sourceTree = "<group>"; };
		2F13C55C2DFBC85800E254FC /* XiaoHongShuOpenSDK.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = XiaoHongShuOpenSDK.xcframework; sourceTree = "<group>"; };
		2F13C55E2DFBC8B100E254FC /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		2F13C5622DFBD47F00E254FC /* XhsUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XhsUtil.h; sourceTree = "<group>"; };
		2F13C5632DFBD47F00E254FC /* XhsUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XhsUtil.m; sourceTree = "<group>"; };
		2F36E21A2E331A6F00E91FEA /* ReceiptHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReceiptHelper.swift; sourceTree = "<group>"; };
		2F36E21C2E33208C00E91FEA /* Runner-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Runner-Bridging-Header.h"; sourceTree = "<group>"; };
		2F6ACDDB2E2DD68C00575DD4 /* WechatOpenSDK-XCFramework.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; path = "WechatOpenSDK-XCFramework.xcframework"; sourceTree = "<group>"; };
		2F849CE12E4D8AC800D40133 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Main.strings"; sourceTree = "<group>"; };
		2F849CE22E4D8AC900D40133 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		2F849CE42E4D8B1900D40133 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = SOURCE_ROOT; };
		2F849CE62E4D8B2700D40133 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/InfoPlist.strings"; sourceTree = SOURCE_ROOT; };
		2F8EBCF92E3B021800CB8157 /* WxStream.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WxStream.h; sourceTree = "<group>"; };
		2F8EBCFA2E3B021800CB8157 /* WxStream.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WxStream.m; sourceTree = "<group>"; };
		2FC44EAD2E0155D4005960EC /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		2FC44EAF2E0155DE005960EC /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		2FC44EB12E0155E7005960EC /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		2FC44EB42E0156F1005960EC /* WxUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = WxUtil.h; sourceTree = "<group>"; };
		2FC44EB52E0156F1005960EC /* WxUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = WxUtil.m; sourceTree = "<group>"; };
		2FD717E02E12765500955D8C /* XhsStream.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = XhsStream.h; sourceTree = "<group>"; };
		2FD717E12E12765500955D8C /* XhsStream.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = XhsStream.m; sourceTree = "<group>"; };
		2FD81F5E2E1CBD5C000EEFB2 /* CrashHandler.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CrashHandler.h; sourceTree = "<group>"; };
		2FD81F5F2E1CBD5C000EEFB2 /* CrashHandler.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CrashHandler.m; sourceTree = "<group>"; };
		331C807B294A618700263BE5 /* RunnerTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RunnerTests.swift; sourceTree = "<group>"; };
		331C8081294A63A400263BE5 /* RunnerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = RunnerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		3B2C29A9CF709352313DD531 /* Pods-RunnerTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release.xcconfig"; sourceTree = "<group>"; };
		3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = AppFrameworkInfo.plist; path = Flutter/AppFrameworkInfo.plist; sourceTree = "<group>"; };
		3EB9EF78431F2293F7D11213 /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		71B8B943FAD3DB8E28CF6765 /* Pods-RunnerTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug.xcconfig"; sourceTree = "<group>"; };
		754F487DC0835FEB3B27ED45 /* Pods-RunnerTests.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile.xcconfig"; sourceTree = "<group>"; };
		7AFA3C8E1D35360C0083082E /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Release.xcconfig; path = Flutter/Release.xcconfig; sourceTree = "<group>"; };
		9340011445A5FEF798D303F4 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		9740EEB21CF90195004384FC /* Debug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Debug.xcconfig; path = Flutter/Debug.xcconfig; sourceTree = "<group>"; };
		9740EEB31CF90195004384FC /* Generated.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Generated.xcconfig; path = Flutter/Generated.xcconfig; sourceTree = "<group>"; };
		97C146EE1CF9000F007C117D /* Runner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Runner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97C146FB1CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		97C146FD1CF9000F007C117D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97C147001CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		97C147021CF9000F007C117D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		9DF2653377DC32A3EDCF6155 /* Pods_RunnerTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_RunnerTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		C91C2D412CA16B210097470E /* Runner.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Runner.entitlements; sourceTree = "<group>"; };
		C94179BD2DA8C90400B1937B /* EspBlufiStream.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EspBlufiStream.h; sourceTree = "<group>"; };
		C94179BE2DA8C90400B1937B /* EspBlufiStream.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EspBlufiStream.m; sourceTree = "<group>"; };
		C94179C02DA8C91800B1937B /* EspBlufiUtil.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = EspBlufiUtil.h; sourceTree = "<group>"; };
		C94179C12DA8C91800B1937B /* EspBlufiUtil.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = EspBlufiUtil.m; sourceTree = "<group>"; };
		C94179C82DA8F12A00B1937B /* ESPPeripheral.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ESPPeripheral.h; sourceTree = "<group>"; };
		C94179C92DA8F12A00B1937B /* ESPPeripheral.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ESPPeripheral.m; sourceTree = "<group>"; };
		C94179CB2DA8FA3100B1937B /* ESPDataConversion.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ESPDataConversion.h; sourceTree = "<group>"; };
		C94179CC2DA8FA3100B1937B /* ESPDataConversion.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ESPDataConversion.m; sourceTree = "<group>"; };
		C94179D12DA9212000B1937B /* BlufiFrameCtrlData.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BlufiFrameCtrlData.h; sourceTree = "<group>"; };
		C94179D22DA9212000B1937B /* BlufiFrameCtrlData.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BlufiFrameCtrlData.m; sourceTree = "<group>"; };
		C94179D32DA9212000B1937B /* BlufiNotifyData.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BlufiNotifyData.h; sourceTree = "<group>"; };
		C94179D42DA9212000B1937B /* BlufiNotifyData.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BlufiNotifyData.m; sourceTree = "<group>"; };
		C94179D62DA9212000B1937B /* BlufiClient.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BlufiClient.m; sourceTree = "<group>"; };
		C94179D72DA9212000B1937B /* BlufiConfigureParams.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BlufiConfigureParams.m; sourceTree = "<group>"; };
		C94179D82DA9212000B1937B /* BlufiConstants.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BlufiConstants.m; sourceTree = "<group>"; };
		C94179D92DA9212000B1937B /* BlufiScanResponse.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BlufiScanResponse.m; sourceTree = "<group>"; };
		C94179DA2DA9212000B1937B /* BlufiStatusResponse.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BlufiStatusResponse.m; sourceTree = "<group>"; };
		C94179DB2DA9212000B1937B /* BlufiVersionResponse.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BlufiVersionResponse.m; sourceTree = "<group>"; };
		C94179DD2DA9212000B1937B /* aes.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = aes.h; sourceTree = "<group>"; };
		C94179DE2DA9212000B1937B /* asn1.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = asn1.h; sourceTree = "<group>"; };
		C94179DF2DA9212000B1937B /* asn1_mac.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = asn1_mac.h; sourceTree = "<group>"; };
		C94179E02DA9212000B1937B /* asn1err.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = asn1err.h; sourceTree = "<group>"; };
		C94179E12DA9212000B1937B /* asn1t.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = asn1t.h; sourceTree = "<group>"; };
		C94179E22DA9212000B1937B /* async.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = async.h; sourceTree = "<group>"; };
		C94179E32DA9212000B1937B /* asyncerr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = asyncerr.h; sourceTree = "<group>"; };
		C94179E42DA9212000B1937B /* bio.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = bio.h; sourceTree = "<group>"; };
		C94179E52DA9212000B1937B /* bioerr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = bioerr.h; sourceTree = "<group>"; };
		C94179E62DA9212000B1937B /* blowfish.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = blowfish.h; sourceTree = "<group>"; };
		C94179E72DA9212000B1937B /* bn.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = bn.h; sourceTree = "<group>"; };
		C94179E82DA9212000B1937B /* bnerr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = bnerr.h; sourceTree = "<group>"; };
		C94179E92DA9212000B1937B /* buffer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = buffer.h; sourceTree = "<group>"; };
		C94179EA2DA9212000B1937B /* buffererr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = buffererr.h; sourceTree = "<group>"; };
		C94179EB2DA9212000B1937B /* camellia.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = camellia.h; sourceTree = "<group>"; };
		C94179EC2DA9212000B1937B /* cast.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = cast.h; sourceTree = "<group>"; };
		C94179ED2DA9212000B1937B /* cmac.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = cmac.h; sourceTree = "<group>"; };
		C94179EE2DA9212000B1937B /* cms.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = cms.h; sourceTree = "<group>"; };
		C94179EF2DA9212000B1937B /* cmserr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = cmserr.h; sourceTree = "<group>"; };
		C94179F02DA9212000B1937B /* comp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = comp.h; sourceTree = "<group>"; };
		C94179F12DA9212000B1937B /* comperr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = comperr.h; sourceTree = "<group>"; };
		C94179F22DA9212000B1937B /* conf.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = conf.h; sourceTree = "<group>"; };
		C94179F32DA9212000B1937B /* conf_api.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = conf_api.h; sourceTree = "<group>"; };
		C94179F42DA9212000B1937B /* conferr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = conferr.h; sourceTree = "<group>"; };
		C94179F52DA9212000B1937B /* crypto.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = crypto.h; sourceTree = "<group>"; };
		C94179F62DA9212000B1937B /* cryptoerr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = cryptoerr.h; sourceTree = "<group>"; };
		C94179F72DA9212000B1937B /* ct.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ct.h; sourceTree = "<group>"; };
		C94179F82DA9212000B1937B /* cterr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = cterr.h; sourceTree = "<group>"; };
		C94179F92DA9212000B1937B /* des.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = des.h; sourceTree = "<group>"; };
		C94179FA2DA9212000B1937B /* dh.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = dh.h; sourceTree = "<group>"; };
		C94179FB2DA9212000B1937B /* dherr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = dherr.h; sourceTree = "<group>"; };
		C94179FC2DA9212000B1937B /* dsa.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = dsa.h; sourceTree = "<group>"; };
		C94179FD2DA9212000B1937B /* dsaerr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = dsaerr.h; sourceTree = "<group>"; };
		C94179FE2DA9212000B1937B /* dtls1.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = dtls1.h; sourceTree = "<group>"; };
		C94179FF2DA9212000B1937B /* e_os2.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = e_os2.h; sourceTree = "<group>"; };
		C9417A002DA9212000B1937B /* ebcdic.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ebcdic.h; sourceTree = "<group>"; };
		C9417A012DA9212000B1937B /* ec.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ec.h; sourceTree = "<group>"; };
		C9417A022DA9212000B1937B /* ecdh.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ecdh.h; sourceTree = "<group>"; };
		C9417A032DA9212000B1937B /* ecdsa.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ecdsa.h; sourceTree = "<group>"; };
		C9417A042DA9212000B1937B /* ecerr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ecerr.h; sourceTree = "<group>"; };
		C9417A052DA9212000B1937B /* engine.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = engine.h; sourceTree = "<group>"; };
		C9417A062DA9212000B1937B /* engineerr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = engineerr.h; sourceTree = "<group>"; };
		C9417A072DA9212000B1937B /* err.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = err.h; sourceTree = "<group>"; };
		C9417A082DA9212000B1937B /* evp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = evp.h; sourceTree = "<group>"; };
		C9417A092DA9212000B1937B /* evperr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = evperr.h; sourceTree = "<group>"; };
		C9417A0A2DA9212000B1937B /* hmac.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = hmac.h; sourceTree = "<group>"; };
		C9417A0B2DA9212000B1937B /* idea.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = idea.h; sourceTree = "<group>"; };
		C9417A0C2DA9212000B1937B /* kdf.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = kdf.h; sourceTree = "<group>"; };
		C9417A0D2DA9212000B1937B /* kdferr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = kdferr.h; sourceTree = "<group>"; };
		C9417A0E2DA9212000B1937B /* lhash.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = lhash.h; sourceTree = "<group>"; };
		C9417A0F2DA9212000B1937B /* md2.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = md2.h; sourceTree = "<group>"; };
		C9417A102DA9212000B1937B /* md4.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = md4.h; sourceTree = "<group>"; };
		C9417A112DA9212000B1937B /* md5.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = md5.h; sourceTree = "<group>"; };
		C9417A122DA9212000B1937B /* mdc2.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = mdc2.h; sourceTree = "<group>"; };
		C9417A132DA9212000B1937B /* modes.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = modes.h; sourceTree = "<group>"; };
		C9417A142DA9212000B1937B /* obj_mac.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = obj_mac.h; sourceTree = "<group>"; };
		C9417A152DA9212000B1937B /* objects.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = objects.h; sourceTree = "<group>"; };
		C9417A162DA9212000B1937B /* objectserr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = objectserr.h; sourceTree = "<group>"; };
		C9417A172DA9212000B1937B /* ocsp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ocsp.h; sourceTree = "<group>"; };
		C9417A182DA9212000B1937B /* ocsperr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ocsperr.h; sourceTree = "<group>"; };
		C9417A192DA9212000B1937B /* opensslconf.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = opensslconf.h; sourceTree = "<group>"; };
		C9417A1A2DA9212000B1937B /* opensslconf_ios_arm64.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = opensslconf_ios_arm64.h; sourceTree = "<group>"; };
		C9417A1B2DA9212000B1937B /* opensslconf_ios_arm64e.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = opensslconf_ios_arm64e.h; sourceTree = "<group>"; };
		C9417A1C2DA9212000B1937B /* opensslconf_ios_x86_64.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = opensslconf_ios_x86_64.h; sourceTree = "<group>"; };
		C9417A1D2DA9212000B1937B /* opensslconf_tvos_arm64.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = opensslconf_tvos_arm64.h; sourceTree = "<group>"; };
		C9417A1E2DA9212000B1937B /* opensslconf_tvos_x86_64.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = opensslconf_tvos_x86_64.h; sourceTree = "<group>"; };
		C9417A1F2DA9212000B1937B /* opensslv.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = opensslv.h; sourceTree = "<group>"; };
		C9417A202DA9212000B1937B /* ossl_typ.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ossl_typ.h; sourceTree = "<group>"; };
		C9417A212DA9212000B1937B /* pem.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = pem.h; sourceTree = "<group>"; };
		C9417A222DA9212000B1937B /* pem2.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = pem2.h; sourceTree = "<group>"; };
		C9417A232DA9212000B1937B /* pemerr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = pemerr.h; sourceTree = "<group>"; };
		C9417A242DA9212000B1937B /* pkcs7.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = pkcs7.h; sourceTree = "<group>"; };
		C9417A252DA9212000B1937B /* pkcs7err.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = pkcs7err.h; sourceTree = "<group>"; };
		C9417A262DA9212000B1937B /* pkcs12.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = pkcs12.h; sourceTree = "<group>"; };
		C9417A272DA9212000B1937B /* pkcs12err.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = pkcs12err.h; sourceTree = "<group>"; };
		C9417A282DA9212000B1937B /* rand.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = rand.h; sourceTree = "<group>"; };
		C9417A292DA9212000B1937B /* rand_drbg.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = rand_drbg.h; sourceTree = "<group>"; };
		C9417A2A2DA9212000B1937B /* randerr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = randerr.h; sourceTree = "<group>"; };
		C9417A2B2DA9212000B1937B /* rc2.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = rc2.h; sourceTree = "<group>"; };
		C9417A2C2DA9212000B1937B /* rc4.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = rc4.h; sourceTree = "<group>"; };
		C9417A2D2DA9212000B1937B /* rc5.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = rc5.h; sourceTree = "<group>"; };
		C9417A2E2DA9212000B1937B /* ripemd.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ripemd.h; sourceTree = "<group>"; };
		C9417A2F2DA9212000B1937B /* rsa.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = rsa.h; sourceTree = "<group>"; };
		C9417A302DA9212000B1937B /* rsaerr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = rsaerr.h; sourceTree = "<group>"; };
		C9417A312DA9212000B1937B /* safestack.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = safestack.h; sourceTree = "<group>"; };
		C9417A322DA9212000B1937B /* seed.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = seed.h; sourceTree = "<group>"; };
		C9417A332DA9212000B1937B /* sha.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = sha.h; sourceTree = "<group>"; };
		C9417A342DA9212000B1937B /* srp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = srp.h; sourceTree = "<group>"; };
		C9417A352DA9212000B1937B /* srtp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = srtp.h; sourceTree = "<group>"; };
		C9417A362DA9212000B1937B /* ssl.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ssl.h; sourceTree = "<group>"; };
		C9417A372DA9212000B1937B /* ssl2.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ssl2.h; sourceTree = "<group>"; };
		C9417A382DA9212000B1937B /* ssl3.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ssl3.h; sourceTree = "<group>"; };
		C9417A392DA9212000B1937B /* sslerr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = sslerr.h; sourceTree = "<group>"; };
		C9417A3A2DA9212000B1937B /* stack.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = stack.h; sourceTree = "<group>"; };
		C9417A3B2DA9212000B1937B /* store.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = store.h; sourceTree = "<group>"; };
		C9417A3C2DA9212000B1937B /* storeerr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = storeerr.h; sourceTree = "<group>"; };
		C9417A3D2DA9212000B1937B /* symhacks.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = symhacks.h; sourceTree = "<group>"; };
		C9417A3E2DA9212000B1937B /* tls1.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = tls1.h; sourceTree = "<group>"; };
		C9417A3F2DA9212000B1937B /* ts.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ts.h; sourceTree = "<group>"; };
		C9417A402DA9212000B1937B /* tserr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = tserr.h; sourceTree = "<group>"; };
		C9417A412DA9212000B1937B /* txt_db.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = txt_db.h; sourceTree = "<group>"; };
		C9417A422DA9212000B1937B /* ui.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ui.h; sourceTree = "<group>"; };
		C9417A432DA9212000B1937B /* uierr.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = uierr.h; sourceTree = "<group>"; };
		C9417A442DA9212000B1937B /* whrlpool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = whrlpool.h; sourceTree = "<group>"; };
		C9417A452DA9212000B1937B /* x509.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = x509.h; sourceTree = "<group>"; };
		C9417A462DA9212000B1937B /* x509_vfy.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = x509_vfy.h; sourceTree = "<group>"; };
		C9417A472DA9212000B1937B /* x509err.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = x509err.h; sourceTree = "<group>"; };
		C9417A482DA9212000B1937B /* x509v3.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = x509v3.h; sourceTree = "<group>"; };
		C9417A492DA9212000B1937B /* x509v3err.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = x509v3err.h; sourceTree = "<group>"; };
		C9417A4B2DA9212000B1937B /* LICENSE */ = {isa = PBXFileReference; lastKnownFileType = text; path = LICENSE; sourceTree = "<group>"; };
		C9417A4C2DA9212000B1937B /* opensslconf-template.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "opensslconf-template.h"; sourceTree = "<group>"; };
		C9417A4E2DA9212000B1937B /* libcrypto.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libcrypto.a; sourceTree = "<group>"; };
		C9417A4F2DA9212000B1937B /* libssl.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libssl.a; sourceTree = "<group>"; };
		C9417A512DA9212000B1937B /* BlufiDH.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BlufiDH.h; sourceTree = "<group>"; };
		C9417A522DA9212000B1937B /* BlufiDH.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BlufiDH.m; sourceTree = "<group>"; };
		C9417A532DA9212000B1937B /* BlufiSecurity.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BlufiSecurity.h; sourceTree = "<group>"; };
		C9417A542DA9212000B1937B /* BlufiSecurity.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BlufiSecurity.m; sourceTree = "<group>"; };
		C9417A562DA9212000B1937B /* BlufiClient.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BlufiClient.h; sourceTree = "<group>"; };
		C9417A572DA9212000B1937B /* BlufiConfigureParams.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BlufiConfigureParams.h; sourceTree = "<group>"; };
		C9417A582DA9212000B1937B /* BlufiConstants.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BlufiConstants.h; sourceTree = "<group>"; };
		C9417A592DA9212000B1937B /* BlufiScanResponse.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BlufiScanResponse.h; sourceTree = "<group>"; };
		C9417A5A2DA9212000B1937B /* BlufiStatusResponse.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BlufiStatusResponse.h; sourceTree = "<group>"; };
		C9417A5B2DA9212000B1937B /* BlufiVersionResponse.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BlufiVersionResponse.h; sourceTree = "<group>"; };
		C9417A5C2DA9212000B1937B /* ESPHeaderFiles.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ESPHeaderFiles.h; sourceTree = "<group>"; };
		C9417A6B2DA924EB00B1937B /* fijkplayer.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = fijkplayer.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		C962DC4F2C856764004E3CF1 /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		C962DC502C856764004E3CF1 /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		C962DC522C856808004E3CF1 /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		C962DC5E2C85A2C1004E3CF1 /* ByteTtsStream.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ByteTtsStream.h; sourceTree = "<group>"; };
		C962DC5F2C85A2C1004E3CF1 /* ByteTtsStream.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ByteTtsStream.m; sourceTree = "<group>"; };
		C962DC622C85A3B8004E3CF1 /* ByteTtsPlugin.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ByteTtsPlugin.h; sourceTree = "<group>"; };
		C962DC632C85A3B8004E3CF1 /* ByteTtsPlugin.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ByteTtsPlugin.m; sourceTree = "<group>"; };
		C962DC652C85A980004E3CF1 /* RegisterPlugin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = RegisterPlugin.h; sourceTree = "<group>"; };
		C962DC662C85A980004E3CF1 /* RegisterPlugin.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RegisterPlugin.m; sourceTree = "<group>"; };
		C962DC682C85B2BB004E3CF1 /* ByteAsrPlugin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ByteAsrPlugin.h; sourceTree = "<group>"; };
		C962DC692C85B2BB004E3CF1 /* ByteAsrPlugin.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ByteAsrPlugin.m; sourceTree = "<group>"; };
		C962DC6B2C85B3DF004E3CF1 /* ByteAsrStream.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ByteAsrStream.h; sourceTree = "<group>"; };
		C962DC6C2C85B3DF004E3CF1 /* ByteAsrStream.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ByteAsrStream.m; sourceTree = "<group>"; };
		C962DC6E2C85B543004E3CF1 /* ByteVolumeStream.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ByteVolumeStream.h; sourceTree = "<group>"; };
		C962DC6F2C85B543004E3CF1 /* ByteVolumeStream.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ByteVolumeStream.m; sourceTree = "<group>"; };
		C962DC712C85C2F1004E3CF1 /* StreamRecorder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StreamRecorder.h; sourceTree = "<group>"; };
		C962DC722C85C2F1004E3CF1 /* StreamRecorder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StreamRecorder.m; sourceTree = "<group>"; };
		C962DC752C87147D004E3CF1 /* libsqlite3.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.tbd; path = usr/lib/libsqlite3.tbd; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		3ED33A44B33B96D2FDC5D26F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				917E44F9C1CC8A647589E905 /* Pods_RunnerTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EB1CF9000F007C117D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				2FC44EAE2E0155D4005960EC /* CoreGraphics.framework in Frameworks */,
				2F13C55D2DFBC85800E254FC /* XiaoHongShuOpenSDK.xcframework in Frameworks */,
				2F6ACDDC2E2DD68C00575DD4 /* WechatOpenSDK-XCFramework.xcframework in Frameworks */,
				2FC44EB22E0155E7005960EC /* WebKit.framework in Frameworks */,
				2F13C55F2DFBC8BB00E254FC /* libz.tbd in Frameworks */,
				2F13C5602DFBC8C400E254FC /* libsqlite3.tbd in Frameworks */,
				2FC44EB02E0155DE005960EC /* Security.framework in Frameworks */,
				59EE6B57F723E270E54DA109 /* Pods_Runner.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2F13C5612DFBD02500E254FC /* XhsUtil */ = {
			isa = PBXGroup;
			children = (
				2F13C5622DFBD47F00E254FC /* XhsUtil.h */,
				2F13C5632DFBD47F00E254FC /* XhsUtil.m */,
				2FD717E02E12765500955D8C /* XhsStream.h */,
				2FD717E12E12765500955D8C /* XhsStream.m */,
			);
			path = XhsUtil;
			sourceTree = "<group>";
		};
		2FC44EB32E0156B6005960EC /* WxUtil */ = {
			isa = PBXGroup;
			children = (
				2FC44EB42E0156F1005960EC /* WxUtil.h */,
				2FC44EB52E0156F1005960EC /* WxUtil.m */,
				2F8EBCF92E3B021800CB8157 /* WxStream.h */,
				2F8EBCFA2E3B021800CB8157 /* WxStream.m */,
			);
			path = WxUtil;
			sourceTree = "<group>";
		};
		331C8082294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXGroup;
			children = (
				331C807B294A618700263BE5 /* RunnerTests.swift */,
			);
			path = RunnerTests;
			sourceTree = "<group>";
		};
		9740EEB11CF90186004384FC /* Flutter */ = {
			isa = PBXGroup;
			children = (
				3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */,
				9740EEB21CF90195004384FC /* Debug.xcconfig */,
				7AFA3C8E1D35360C0083082E /* Release.xcconfig */,
				9740EEB31CF90195004384FC /* Generated.xcconfig */,
			);
			name = Flutter;
			sourceTree = "<group>";
		};
		97C146E51CF9000F007C117D = {
			isa = PBXGroup;
			children = (
				C9417A5D2DA9212000B1937B /* BlufiLibrary */,
				9740EEB11CF90186004384FC /* Flutter */,
				97C146F01CF9000F007C117D /* Runner */,
				97C146EF1CF9000F007C117D /* Products */,
				331C8082294A63A400263BE5 /* RunnerTests */,
				FA1B4E6D7F4C0FE3D2D7221C /* Pods */,
				E748A16D3129695BAAB7B0F6 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		97C146EF1CF9000F007C117D /* Products */ = {
			isa = PBXGroup;
			children = (
				97C146EE1CF9000F007C117D /* Runner.app */,
				331C8081294A63A400263BE5 /* RunnerTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97C146F01CF9000F007C117D /* Runner */ = {
			isa = PBXGroup;
			children = (
				C91C2D412CA16B210097470E /* Runner.entitlements */,
				C962DC552C856B1A004E3CF1 /* Classes */,
				C962DC522C856808004E3CF1 /* main.m */,
				C962DC4F2C856764004E3CF1 /* AppDelegate.h */,
				C962DC502C856764004E3CF1 /* AppDelegate.m */,
				97C146FA1CF9000F007C117D /* Main.storyboard */,
				97C146FD1CF9000F007C117D /* Assets.xcassets */,
				97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */,
				97C147021CF9000F007C117D /* Info.plist */,
				1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */,
				1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */,
				2FD81F5E2E1CBD5C000EEFB2 /* CrashHandler.h */,
				2FD81F5F2E1CBD5C000EEFB2 /* CrashHandler.m */,
				2F36E21C2E33208C00E91FEA /* Runner-Bridging-Header.h */,
				2F849CE32E4D8B1900D40133 /* InfoPlist.strings */,
			);
			path = Runner;
			sourceTree = "<group>";
		};
		C94179B92DA8C6D100B1937B /* EspBlufiUtil */ = {
			isa = PBXGroup;
			children = (
				C94179CB2DA8FA3100B1937B /* ESPDataConversion.h */,
				C94179CC2DA8FA3100B1937B /* ESPDataConversion.m */,
				C94179BD2DA8C90400B1937B /* EspBlufiStream.h */,
				C94179BE2DA8C90400B1937B /* EspBlufiStream.m */,
				C94179C02DA8C91800B1937B /* EspBlufiUtil.h */,
				C94179C12DA8C91800B1937B /* EspBlufiUtil.m */,
				C94179C82DA8F12A00B1937B /* ESPPeripheral.h */,
				C94179C92DA8F12A00B1937B /* ESPPeripheral.m */,
			);
			path = EspBlufiUtil;
			sourceTree = "<group>";
		};
		C94179D52DA9212000B1937B /* Data */ = {
			isa = PBXGroup;
			children = (
				C94179D12DA9212000B1937B /* BlufiFrameCtrlData.h */,
				C94179D22DA9212000B1937B /* BlufiFrameCtrlData.m */,
				C94179D32DA9212000B1937B /* BlufiNotifyData.h */,
				C94179D42DA9212000B1937B /* BlufiNotifyData.m */,
			);
			path = Data;
			sourceTree = "<group>";
		};
		C94179DC2DA9212000B1937B /* Response */ = {
			isa = PBXGroup;
			children = (
				C94179D62DA9212000B1937B /* BlufiClient.m */,
				C94179D72DA9212000B1937B /* BlufiConfigureParams.m */,
				C94179D82DA9212000B1937B /* BlufiConstants.m */,
				C94179D92DA9212000B1937B /* BlufiScanResponse.m */,
				C94179DA2DA9212000B1937B /* BlufiStatusResponse.m */,
				C94179DB2DA9212000B1937B /* BlufiVersionResponse.m */,
			);
			path = Response;
			sourceTree = "<group>";
		};
		C9417A4A2DA9212000B1937B /* openssl */ = {
			isa = PBXGroup;
			children = (
				C94179DD2DA9212000B1937B /* aes.h */,
				C94179DE2DA9212000B1937B /* asn1.h */,
				C94179DF2DA9212000B1937B /* asn1_mac.h */,
				C94179E02DA9212000B1937B /* asn1err.h */,
				C94179E12DA9212000B1937B /* asn1t.h */,
				C94179E22DA9212000B1937B /* async.h */,
				C94179E32DA9212000B1937B /* asyncerr.h */,
				C94179E42DA9212000B1937B /* bio.h */,
				C94179E52DA9212000B1937B /* bioerr.h */,
				C94179E62DA9212000B1937B /* blowfish.h */,
				C94179E72DA9212000B1937B /* bn.h */,
				C94179E82DA9212000B1937B /* bnerr.h */,
				C94179E92DA9212000B1937B /* buffer.h */,
				C94179EA2DA9212000B1937B /* buffererr.h */,
				C94179EB2DA9212000B1937B /* camellia.h */,
				C94179EC2DA9212000B1937B /* cast.h */,
				C94179ED2DA9212000B1937B /* cmac.h */,
				C94179EE2DA9212000B1937B /* cms.h */,
				C94179EF2DA9212000B1937B /* cmserr.h */,
				C94179F02DA9212000B1937B /* comp.h */,
				C94179F12DA9212000B1937B /* comperr.h */,
				C94179F22DA9212000B1937B /* conf.h */,
				C94179F32DA9212000B1937B /* conf_api.h */,
				C94179F42DA9212000B1937B /* conferr.h */,
				C94179F52DA9212000B1937B /* crypto.h */,
				C94179F62DA9212000B1937B /* cryptoerr.h */,
				C94179F72DA9212000B1937B /* ct.h */,
				C94179F82DA9212000B1937B /* cterr.h */,
				C94179F92DA9212000B1937B /* des.h */,
				C94179FA2DA9212000B1937B /* dh.h */,
				C94179FB2DA9212000B1937B /* dherr.h */,
				C94179FC2DA9212000B1937B /* dsa.h */,
				C94179FD2DA9212000B1937B /* dsaerr.h */,
				C94179FE2DA9212000B1937B /* dtls1.h */,
				C94179FF2DA9212000B1937B /* e_os2.h */,
				C9417A002DA9212000B1937B /* ebcdic.h */,
				C9417A012DA9212000B1937B /* ec.h */,
				C9417A022DA9212000B1937B /* ecdh.h */,
				C9417A032DA9212000B1937B /* ecdsa.h */,
				C9417A042DA9212000B1937B /* ecerr.h */,
				C9417A052DA9212000B1937B /* engine.h */,
				C9417A062DA9212000B1937B /* engineerr.h */,
				C9417A072DA9212000B1937B /* err.h */,
				C9417A082DA9212000B1937B /* evp.h */,
				C9417A092DA9212000B1937B /* evperr.h */,
				C9417A0A2DA9212000B1937B /* hmac.h */,
				C9417A0B2DA9212000B1937B /* idea.h */,
				C9417A0C2DA9212000B1937B /* kdf.h */,
				C9417A0D2DA9212000B1937B /* kdferr.h */,
				C9417A0E2DA9212000B1937B /* lhash.h */,
				C9417A0F2DA9212000B1937B /* md2.h */,
				C9417A102DA9212000B1937B /* md4.h */,
				C9417A112DA9212000B1937B /* md5.h */,
				C9417A122DA9212000B1937B /* mdc2.h */,
				C9417A132DA9212000B1937B /* modes.h */,
				C9417A142DA9212000B1937B /* obj_mac.h */,
				C9417A152DA9212000B1937B /* objects.h */,
				C9417A162DA9212000B1937B /* objectserr.h */,
				C9417A172DA9212000B1937B /* ocsp.h */,
				C9417A182DA9212000B1937B /* ocsperr.h */,
				C9417A192DA9212000B1937B /* opensslconf.h */,
				C9417A1A2DA9212000B1937B /* opensslconf_ios_arm64.h */,
				C9417A1B2DA9212000B1937B /* opensslconf_ios_arm64e.h */,
				C9417A1C2DA9212000B1937B /* opensslconf_ios_x86_64.h */,
				C9417A1D2DA9212000B1937B /* opensslconf_tvos_arm64.h */,
				C9417A1E2DA9212000B1937B /* opensslconf_tvos_x86_64.h */,
				C9417A1F2DA9212000B1937B /* opensslv.h */,
				C9417A202DA9212000B1937B /* ossl_typ.h */,
				C9417A212DA9212000B1937B /* pem.h */,
				C9417A222DA9212000B1937B /* pem2.h */,
				C9417A232DA9212000B1937B /* pemerr.h */,
				C9417A242DA9212000B1937B /* pkcs7.h */,
				C9417A252DA9212000B1937B /* pkcs7err.h */,
				C9417A262DA9212000B1937B /* pkcs12.h */,
				C9417A272DA9212000B1937B /* pkcs12err.h */,
				C9417A282DA9212000B1937B /* rand.h */,
				C9417A292DA9212000B1937B /* rand_drbg.h */,
				C9417A2A2DA9212000B1937B /* randerr.h */,
				C9417A2B2DA9212000B1937B /* rc2.h */,
				C9417A2C2DA9212000B1937B /* rc4.h */,
				C9417A2D2DA9212000B1937B /* rc5.h */,
				C9417A2E2DA9212000B1937B /* ripemd.h */,
				C9417A2F2DA9212000B1937B /* rsa.h */,
				C9417A302DA9212000B1937B /* rsaerr.h */,
				C9417A312DA9212000B1937B /* safestack.h */,
				C9417A322DA9212000B1937B /* seed.h */,
				C9417A332DA9212000B1937B /* sha.h */,
				C9417A342DA9212000B1937B /* srp.h */,
				C9417A352DA9212000B1937B /* srtp.h */,
				C9417A362DA9212000B1937B /* ssl.h */,
				C9417A372DA9212000B1937B /* ssl2.h */,
				C9417A382DA9212000B1937B /* ssl3.h */,
				C9417A392DA9212000B1937B /* sslerr.h */,
				C9417A3A2DA9212000B1937B /* stack.h */,
				C9417A3B2DA9212000B1937B /* store.h */,
				C9417A3C2DA9212000B1937B /* storeerr.h */,
				C9417A3D2DA9212000B1937B /* symhacks.h */,
				C9417A3E2DA9212000B1937B /* tls1.h */,
				C9417A3F2DA9212000B1937B /* ts.h */,
				C9417A402DA9212000B1937B /* tserr.h */,
				C9417A412DA9212000B1937B /* txt_db.h */,
				C9417A422DA9212000B1937B /* ui.h */,
				C9417A432DA9212000B1937B /* uierr.h */,
				C9417A442DA9212000B1937B /* whrlpool.h */,
				C9417A452DA9212000B1937B /* x509.h */,
				C9417A462DA9212000B1937B /* x509_vfy.h */,
				C9417A472DA9212000B1937B /* x509err.h */,
				C9417A482DA9212000B1937B /* x509v3.h */,
				C9417A492DA9212000B1937B /* x509v3err.h */,
			);
			path = openssl;
			sourceTree = "<group>";
		};
		C9417A4D2DA9212000B1937B /* include */ = {
			isa = PBXGroup;
			children = (
				C9417A4A2DA9212000B1937B /* openssl */,
				C9417A4B2DA9212000B1937B /* LICENSE */,
				C9417A4C2DA9212000B1937B /* opensslconf-template.h */,
			);
			path = include;
			sourceTree = "<group>";
		};
		C9417A502DA9212000B1937B /* openssl */ = {
			isa = PBXGroup;
			children = (
				C9417A4D2DA9212000B1937B /* include */,
				C9417A4E2DA9212000B1937B /* libcrypto.a */,
				C9417A4F2DA9212000B1937B /* libssl.a */,
			);
			path = openssl;
			sourceTree = "<group>";
		};
		C9417A552DA9212000B1937B /* Security */ = {
			isa = PBXGroup;
			children = (
				C9417A502DA9212000B1937B /* openssl */,
				C9417A512DA9212000B1937B /* BlufiDH.h */,
				C9417A522DA9212000B1937B /* BlufiDH.m */,
				C9417A532DA9212000B1937B /* BlufiSecurity.h */,
				C9417A542DA9212000B1937B /* BlufiSecurity.m */,
			);
			path = Security;
			sourceTree = "<group>";
		};
		C9417A5D2DA9212000B1937B /* BlufiLibrary */ = {
			isa = PBXGroup;
			children = (
				C94179D52DA9212000B1937B /* Data */,
				C94179DC2DA9212000B1937B /* Response */,
				C9417A552DA9212000B1937B /* Security */,
				C9417A562DA9212000B1937B /* BlufiClient.h */,
				C9417A572DA9212000B1937B /* BlufiConfigureParams.h */,
				C9417A582DA9212000B1937B /* BlufiConstants.h */,
				C9417A592DA9212000B1937B /* BlufiScanResponse.h */,
				C9417A5A2DA9212000B1937B /* BlufiStatusResponse.h */,
				C9417A5B2DA9212000B1937B /* BlufiVersionResponse.h */,
				C9417A5C2DA9212000B1937B /* ESPHeaderFiles.h */,
			);
			path = BlufiLibrary;
			sourceTree = "<group>";
		};
		C962DC552C856B1A004E3CF1 /* Classes */ = {
			isa = PBXGroup;
			children = (
				2FC44EB32E0156B6005960EC /* WxUtil */,
				C94179B92DA8C6D100B1937B /* EspBlufiUtil */,
				C962DC732C85C2F1004E3CF1 /* utils */,
				C962DC602C85A2C1004E3CF1 /* ByteSpeechStream */,
				C962DC5A2C856C65004E3CF1 /* ByteAsrPlugin */,
				C962DC592C856C65004E3CF1 /* ByteTtsPlugin */,
				C962DC652C85A980004E3CF1 /* RegisterPlugin.h */,
				C962DC662C85A980004E3CF1 /* RegisterPlugin.m */,
				2F13C5612DFBD02500E254FC /* XhsUtil */,
				2F36E21A2E331A6F00E91FEA /* ReceiptHelper.swift */,
			);
			path = Classes;
			sourceTree = SOURCE_ROOT;
		};
		C962DC592C856C65004E3CF1 /* ByteTtsPlugin */ = {
			isa = PBXGroup;
			children = (
				C962DC622C85A3B8004E3CF1 /* ByteTtsPlugin.h */,
				C962DC632C85A3B8004E3CF1 /* ByteTtsPlugin.m */,
			);
			path = ByteTtsPlugin;
			sourceTree = "<group>";
		};
		C962DC5A2C856C65004E3CF1 /* ByteAsrPlugin */ = {
			isa = PBXGroup;
			children = (
				C962DC682C85B2BB004E3CF1 /* ByteAsrPlugin.h */,
				C962DC692C85B2BB004E3CF1 /* ByteAsrPlugin.m */,
			);
			path = ByteAsrPlugin;
			sourceTree = "<group>";
		};
		C962DC602C85A2C1004E3CF1 /* ByteSpeechStream */ = {
			isa = PBXGroup;
			children = (
				C962DC5E2C85A2C1004E3CF1 /* ByteTtsStream.h */,
				C962DC5F2C85A2C1004E3CF1 /* ByteTtsStream.m */,
				C962DC6B2C85B3DF004E3CF1 /* ByteAsrStream.h */,
				C962DC6C2C85B3DF004E3CF1 /* ByteAsrStream.m */,
				C962DC6E2C85B543004E3CF1 /* ByteVolumeStream.h */,
				C962DC6F2C85B543004E3CF1 /* ByteVolumeStream.m */,
			);
			path = ByteSpeechStream;
			sourceTree = "<group>";
		};
		C962DC732C85C2F1004E3CF1 /* utils */ = {
			isa = PBXGroup;
			children = (
				C962DC712C85C2F1004E3CF1 /* StreamRecorder.h */,
				C962DC722C85C2F1004E3CF1 /* StreamRecorder.m */,
			);
			path = utils;
			sourceTree = "<group>";
		};
		E748A16D3129695BAAB7B0F6 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				2F6ACDDB2E2DD68C00575DD4 /* WechatOpenSDK-XCFramework.xcframework */,
				2FC44EB12E0155E7005960EC /* WebKit.framework */,
				2FC44EAF2E0155DE005960EC /* Security.framework */,
				2FC44EAD2E0155D4005960EC /* CoreGraphics.framework */,
				2F13C55E2DFBC8B100E254FC /* libz.tbd */,
				2F13C55C2DFBC85800E254FC /* XiaoHongShuOpenSDK.xcframework */,
				C9417A6B2DA924EB00B1937B /* fijkplayer.framework */,
				C962DC752C87147D004E3CF1 /* libsqlite3.tbd */,
				12792590A185A705FC447430 /* Pods_Runner.framework */,
				9DF2653377DC32A3EDCF6155 /* Pods_RunnerTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		FA1B4E6D7F4C0FE3D2D7221C /* Pods */ = {
			isa = PBXGroup;
			children = (
				9340011445A5FEF798D303F4 /* Pods-Runner.debug.xcconfig */,
				3EB9EF78431F2293F7D11213 /* Pods-Runner.release.xcconfig */,
				0E8445970BAC87AF95E1DE26 /* Pods-Runner.profile.xcconfig */,
				71B8B943FAD3DB8E28CF6765 /* Pods-RunnerTests.debug.xcconfig */,
				3B2C29A9CF709352313DD531 /* Pods-RunnerTests.release.xcconfig */,
				754F487DC0835FEB3B27ED45 /* Pods-RunnerTests.profile.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		331C8080294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 331C8087294A63A400263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */;
			buildPhases = (
				E94570A668A3B9E962B5E2C2 /* [CP] Check Pods Manifest.lock */,
				331C807D294A63A400263BE5 /* Sources */,
				331C807F294A63A400263BE5 /* Resources */,
				3ED33A44B33B96D2FDC5D26F /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				331C8086294A63A400263BE5 /* PBXTargetDependency */,
			);
			name = RunnerTests;
			productName = RunnerTests;
			productReference = 331C8081294A63A400263BE5 /* RunnerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		97C146ED1CF9000F007C117D /* Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */;
			buildPhases = (
				899D8C8AC1B6EED5FA4D4207 /* [CP] Check Pods Manifest.lock */,
				9740EEB61CF901F6004384FC /* Run Script */,
				97C146EA1CF9000F007C117D /* Sources */,
				97C146EB1CF9000F007C117D /* Frameworks */,
				97C146EC1CF9000F007C117D /* Resources */,
				3B06AD1E1E4923F5004D2608 /* Thin Binary */,
				DEB672355985B63412BC7910 /* [CP] Embed Pods Frameworks */,
				9AD91B6995190F6DDC9902A5 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Runner;
			productName = Runner;
			productReference = 97C146EE1CF9000F007C117D /* Runner.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97C146E61CF9000F007C117D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				KnownAssetTags = (
					English,
				);
				LastUpgradeCheck = 1510;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					331C8080294A63A400263BE5 = {
						CreatedOnToolsVersion = 14.0;
						TestTargetID = 97C146ED1CF9000F007C117D;
					};
					97C146ED1CF9000F007C117D = {
						CreatedOnToolsVersion = 7.3.1;
						LastSwiftMigration = 1100;
					};
				};
			};
			buildConfigurationList = 97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 97C146E51CF9000F007C117D;
			productRefGroup = 97C146EF1CF9000F007C117D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97C146ED1CF9000F007C117D /* Runner */,
				331C8080294A63A400263BE5 /* RunnerTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		331C807F294A63A400263BE5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EC1CF9000F007C117D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */,
				2F849CE52E4D8B1900D40133 /* InfoPlist.strings in Resources */,
				3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */,
				97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */,
				97C146FC1CF9000F007C117D /* Main.storyboard in Resources */,
				C9417A682DA9212000B1937B /* LICENSE in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		3B06AD1E1E4923F5004D2608 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin\n";
		};
		899D8C8AC1B6EED5FA4D4207 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9740EEB61CF901F6004384FC /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build\n";
		};
		9AD91B6995190F6DDC9902A5 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		DEB672355985B63412BC7910 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E94570A668A3B9E962B5E2C2 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RunnerTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		331C807D294A63A400263BE5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				331C808B294A63AB00263BE5 /* RunnerTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EA1CF9000F007C117D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				C94179CD2DA8FA3100B1937B /* ESPDataConversion.m in Sources */,
				C94179C22DA8C91800B1937B /* EspBlufiUtil.m in Sources */,
				2F8EBCFB2E3B021800CB8157 /* WxStream.m in Sources */,
				C962DC6D2C85B3DF004E3CF1 /* ByteAsrStream.m in Sources */,
				C962DC612C85A2C1004E3CF1 /* ByteTtsStream.m in Sources */,
				C962DC512C856764004E3CF1 /* AppDelegate.m in Sources */,
				2F13C5642DFBD47F00E254FC /* XhsUtil.m in Sources */,
				C962DC672C85A980004E3CF1 /* RegisterPlugin.m in Sources */,
				C962DC642C85A3B8004E3CF1 /* ByteTtsPlugin.m in Sources */,
				2FD81F602E1CBD5C000EEFB2 /* CrashHandler.m in Sources */,
				C94179BF2DA8C90400B1937B /* EspBlufiStream.m in Sources */,
				C962DC532C856808004E3CF1 /* main.m in Sources */,
				C962DC6A2C85B2BB004E3CF1 /* ByteAsrPlugin.m in Sources */,
				C962DC702C85B543004E3CF1 /* ByteVolumeStream.m in Sources */,
				C94179CA2DA8F12A00B1937B /* ESPPeripheral.m in Sources */,
				1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */,
				C9417A5E2DA9212000B1937B /* BlufiConstants.m in Sources */,
				2FD717E22E12765500955D8C /* XhsStream.m in Sources */,
				C9417A5F2DA9212000B1937B /* BlufiVersionResponse.m in Sources */,
				C9417A602DA9212000B1937B /* BlufiSecurity.m in Sources */,
				2F36E21B2E331A6F00E91FEA /* ReceiptHelper.swift in Sources */,
				C9417A612DA9212000B1937B /* BlufiNotifyData.m in Sources */,
				2FC44EB62E0156F1005960EC /* WxUtil.m in Sources */,
				C9417A622DA9212000B1937B /* BlufiStatusResponse.m in Sources */,
				C9417A632DA9212000B1937B /* BlufiFrameCtrlData.m in Sources */,
				C9417A642DA9212000B1937B /* BlufiClient.m in Sources */,
				C9417A652DA9212000B1937B /* BlufiDH.m in Sources */,
				C9417A662DA9212000B1937B /* BlufiScanResponse.m in Sources */,
				C9417A672DA9212000B1937B /* BlufiConfigureParams.m in Sources */,
				C962DC742C85C2F1004E3CF1 /* StreamRecorder.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		331C8086294A63A400263BE5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 97C146ED1CF9000F007C117D /* Runner */;
			targetProxy = 331C8085294A63A400263BE5 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		2F849CE32E4D8B1900D40133 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				2F849CE42E4D8B1900D40133 /* en */,
				2F849CE62E4D8B2700D40133 /* zh-Hans */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		97C146FA1CF9000F007C117D /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C146FB1CF9000F007C117D /* Base */,
				2F849CE12E4D8AC800D40133 /* zh-Hans */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C147001CF9000F007C117D /* Base */,
				2F849CE22E4D8AC900D40133 /* zh-Hans */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		249021D3217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		249021D4217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = YES;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = VZVK4PX5X3;
				ENABLE_BITCODE = NO;
				ENABLE_ONLY_ACTIVE_RESOURCES = NO;
				FLUTTER_BUILD_NAME = 1.7.0;
				FLUTTER_BUILD_NUMBER = 14;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/BlufiLibrary/Security/openssl/include/**",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/SpeechEngineToB/ios-arch-iphone\"",
					"\"${PODS_ROOT}/SpeechEngineToB/pod/libs\"",
					"\"${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}\"",
					/usr/lib/swift,
					"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)/",
					"$(SDKROOT)/usr/lib/swift",
					"$(PROJECT_DIR)/BlufiLibrary/Security/openssl/**",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-all_load",
					"-l\"SpeechEngineToB_SpeechEngineToB_awesome_ios\"",
					"-l\"bz2\"",
					"-l\"c++\"",
					"-l\"speechsdk\"",
					"-l\"sqlite3\"",
					"-l\"stdc++\"",
					"-l\"z\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"AVKit\"",
					"-framework",
					"\"AudioToolbox\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreMedia\"",
					"-framework",
					"\"CoreVideo\"",
					"-framework",
					"\"DKImagePickerController\"",
					"-framework",
					"\"DKPhotoGallery\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"JavaScriptCore\"",
					"-framework",
					"\"MediaPlayer\"",
					"-framework",
					"\"MobileCoreServices\"",
					"-framework",
					"\"OpenGLES\"",
					"-framework",
					"\"Photos\"",
					"-framework",
					"\"PhotosUI\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"RealXBase\"",
					"-framework",
					"\"SDWebImage\"",
					"-framework",
					"\"SpeechEngineToB\"",
					"-framework",
					"\"SwiftyGif\"",
					"-framework",
					"\"TOCropViewController\"",
					"-framework",
					"\"TTNetworkManager\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"VideoToolbox\"",
					"-framework",
					"\"VolcEngineRTC\"",
					"-framework",
					"\"audio_session\"",
					"-framework",
					"\"device_info_plus\"",
					"-framework",
					"\"file_picker\"",
					"-framework",
					"\"flutter_downloader\"",
					"-framework",
					"\"fluttertoast\"",
					"-framework",
					"\"image_cropper\"",
					"-framework",
					"\"image_gallery_saver\"",
					"-framework",
					"\"just_audio\"",
					"-framework",
					"\"open_file_ios\"",
					"-framework",
					"\"package_info_plus\"",
					"-framework",
					"\"path_provider_foundation\"",
					"-framework",
					"\"permission_handler_apple\"",
					"-framework",
					"\"photo_manager\"",
					"-framework",
					"\"shared_preferences_foundation\"",
					"-framework",
					"\"sqflite_darwin\"",
					"-framework",
					"\"url_launcher_ios\"",
					"-framework",
					"\"video_player_avfoundation\"",
					"-framework",
					"\"volc_engine_rtc\"",
					"-framework",
					"\"webview_flutter_wkwebview\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.marspt.xiaopa;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		331C8088294A63A400263BE5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 71B8B943FAD3DB8E28CF6765 /* Pods-RunnerTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = VZVK4PX5X3;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.marspt.xiaopa;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Debug;
		};
		331C8089294A63A400263BE5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3B2C29A9CF709352313DD531 /* Pods-RunnerTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = VZVK4PX5X3;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.marspt.xiaopa;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Release;
		};
		331C808A294A63A400263BE5 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 754F487DC0835FEB3B27ED45 /* Pods-RunnerTests.profile.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = VZVK4PX5X3;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.marspt.xiaopa;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Profile;
		};
		97C147031CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		97C147041CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = AppIcon;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		97C147061CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = YES;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = VZVK4PX5X3;
				ENABLE_BITCODE = NO;
				ENABLE_ONLY_ACTIVE_RESOURCES = NO;
				FLUTTER_BUILD_NAME = 1.7.0;
				FLUTTER_BUILD_NUMBER = 14;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/BlufiLibrary/Security/openssl/include/**",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/SpeechEngineToB/ios-arch-iphone\"",
					"\"${PODS_ROOT}/SpeechEngineToB/pod/libs\"",
					"\"${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}\"",
					/usr/lib/swift,
					"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)/",
					"$(SDKROOT)/usr/lib/swift",
					"$(PROJECT_DIR)/BlufiLibrary/Security/openssl/**",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-all_load",
					"-l\"SpeechEngineToB_SpeechEngineToB_awesome_ios\"",
					"-l\"bz2\"",
					"-l\"c++\"",
					"-l\"speechsdk\"",
					"-l\"sqlite3\"",
					"-l\"stdc++\"",
					"-l\"z\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"AVKit\"",
					"-framework",
					"\"AudioToolbox\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreMedia\"",
					"-framework",
					"\"CoreVideo\"",
					"-framework",
					"\"DKImagePickerController\"",
					"-framework",
					"\"DKPhotoGallery\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"JavaScriptCore\"",
					"-framework",
					"\"MediaPlayer\"",
					"-framework",
					"\"MobileCoreServices\"",
					"-framework",
					"\"OpenGLES\"",
					"-framework",
					"\"Photos\"",
					"-framework",
					"\"PhotosUI\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"RealXBase\"",
					"-framework",
					"\"SDWebImage\"",
					"-framework",
					"\"SpeechEngineToB\"",
					"-framework",
					"\"SwiftyGif\"",
					"-framework",
					"\"TOCropViewController\"",
					"-framework",
					"\"TTNetworkManager\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"VideoToolbox\"",
					"-framework",
					"\"VolcEngineRTC\"",
					"-framework",
					"\"audio_session\"",
					"-framework",
					"\"device_info_plus\"",
					"-framework",
					"\"file_picker\"",
					"-framework",
					"\"flutter_downloader\"",
					"-framework",
					"\"fluttertoast\"",
					"-framework",
					"\"image_cropper\"",
					"-framework",
					"\"image_gallery_saver\"",
					"-framework",
					"\"just_audio\"",
					"-framework",
					"\"open_file_ios\"",
					"-framework",
					"\"package_info_plus\"",
					"-framework",
					"\"path_provider_foundation\"",
					"-framework",
					"\"permission_handler_apple\"",
					"-framework",
					"\"photo_manager\"",
					"-framework",
					"\"shared_preferences_foundation\"",
					"-framework",
					"\"sqflite_darwin\"",
					"-framework",
					"\"url_launcher_ios\"",
					"-framework",
					"\"video_player_avfoundation\"",
					"-framework",
					"\"volc_engine_rtc\"",
					"-framework",
					"\"webview_flutter_wkwebview\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.marspt.xiaopa;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		97C147071CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = YES;
				CURRENT_PROJECT_VERSION = "$(FLUTTER_BUILD_NUMBER)";
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = VZVK4PX5X3;
				ENABLE_BITCODE = NO;
				ENABLE_ONLY_ACTIVE_RESOURCES = NO;
				FLUTTER_BUILD_NAME = 1.7.0;
				FLUTTER_BUILD_NUMBER = 14;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(SRCROOT)/BlufiLibrary/Security/openssl/include/**",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/SpeechEngineToB/ios-arch-iphone\"",
					"\"${PODS_ROOT}/SpeechEngineToB/pod/libs\"",
					"\"${TOOLCHAIN_DIR}/usr/lib/swift/${PLATFORM_NAME}\"",
					/usr/lib/swift,
					"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)/",
					"$(SDKROOT)/usr/lib/swift",
					"$(PROJECT_DIR)/BlufiLibrary/Security/openssl/**",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-all_load",
					"-l\"SpeechEngineToB_SpeechEngineToB_awesome_ios\"",
					"-l\"bz2\"",
					"-l\"c++\"",
					"-l\"speechsdk\"",
					"-l\"sqlite3\"",
					"-l\"stdc++\"",
					"-l\"z\"",
					"-framework",
					"\"AVFoundation\"",
					"-framework",
					"\"AVKit\"",
					"-framework",
					"\"AudioToolbox\"",
					"-framework",
					"\"CoreGraphics\"",
					"-framework",
					"\"CoreMedia\"",
					"-framework",
					"\"CoreVideo\"",
					"-framework",
					"\"DKImagePickerController\"",
					"-framework",
					"\"DKPhotoGallery\"",
					"-framework",
					"\"Foundation\"",
					"-framework",
					"\"ImageIO\"",
					"-framework",
					"\"JavaScriptCore\"",
					"-framework",
					"\"MediaPlayer\"",
					"-framework",
					"\"MobileCoreServices\"",
					"-framework",
					"\"OpenGLES\"",
					"-framework",
					"\"Photos\"",
					"-framework",
					"\"PhotosUI\"",
					"-framework",
					"\"QuartzCore\"",
					"-framework",
					"\"RealXBase\"",
					"-framework",
					"\"SDWebImage\"",
					"-framework",
					"\"SpeechEngineToB\"",
					"-framework",
					"\"SwiftyGif\"",
					"-framework",
					"\"TOCropViewController\"",
					"-framework",
					"\"TTNetworkManager\"",
					"-framework",
					"\"UIKit\"",
					"-framework",
					"\"VideoToolbox\"",
					"-framework",
					"\"VolcEngineRTC\"",
					"-framework",
					"\"audio_session\"",
					"-framework",
					"\"device_info_plus\"",
					"-framework",
					"\"file_picker\"",
					"-framework",
					"\"flutter_downloader\"",
					"-framework",
					"\"fluttertoast\"",
					"-framework",
					"\"image_cropper\"",
					"-framework",
					"\"image_gallery_saver\"",
					"-framework",
					"\"just_audio\"",
					"-framework",
					"\"open_file_ios\"",
					"-framework",
					"\"package_info_plus\"",
					"-framework",
					"\"path_provider_foundation\"",
					"-framework",
					"\"permission_handler_apple\"",
					"-framework",
					"\"photo_manager\"",
					"-framework",
					"\"shared_preferences_foundation\"",
					"-framework",
					"\"sqflite_darwin\"",
					"-framework",
					"\"url_launcher_ios\"",
					"-framework",
					"\"video_player_avfoundation\"",
					"-framework",
					"\"volc_engine_rtc\"",
					"-framework",
					"\"webview_flutter_wkwebview\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.marspt.xiaopa;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		331C8087294A63A400263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				331C8088294A63A400263BE5 /* Debug */,
				331C8089294A63A400263BE5 /* Release */,
				331C808A294A63A400263BE5 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147031CF9000F007C117D /* Debug */,
				97C147041CF9000F007C117D /* Release */,
				249021D3217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147061CF9000F007C117D /* Debug */,
				97C147071CF9000F007C117D /* Release */,
				249021D4217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 97C146E61CF9000F007C117D /* Project object */;
}
